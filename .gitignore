# Learn more https://docs.github.com/en/get-started/getting-started-with-git/ignoring-files

# dependencies
node_modules/

# Expo
.expo/
dist/
web-build/
expo-env.d.ts

# Native
*.orig.*
*.jks
*.p8
*.p12
*.key
*.mobileprovision

# Metro
.metro-health-check*

# debug
npm-debug.*
yarn-debug.*
yarn-error.*

# macOS
.DS_Store
*.pem

# local env files
.env*.local

# typescript
*.tsbuildinfo

app-example
.aider*
.vscode/
.idea/

# ios/
# android/

# secrets
.env
.env.production
.env.development

android/key.properties
api-7015365313464935485-388275-d59fe4433077.json
android/app/google-services.json
ios/GoogleService-Info.plist
ios/DiogenesAICompanion/GoogleService-Info.plist
GoogleService-Info.plist
google-services.json

.env.local
