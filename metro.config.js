const { getDefaultConfig, mergeConfig } = require('@react-native/metro-config');
const { getSentryExpoConfig } = require('@sentry/react-native/metro');

// Get the default Metro config
const metroConfig = getDefaultConfig(__dirname);

// Get the Sentry config
const sentryConfig = getSentryExpoConfig(__dirname);

// Merge the default config with the Sentry config
const config = mergeConfig(metroConfig, sentryConfig);

module.exports = config;
