# Local LLM Integration Design Document

## Executive Summary

This document outlines the design and implementation strategy for integrating local Large Language Model (LLM) capabilities into our React Native application, based on analysis of the PocketPal AI codebase. PocketPal AI demonstrates a mature, production-ready approach to running LLMs locally on mobile devices using the `@pocketpalai/llama.rn` library, which provides React Native bindings for llama.cpp.

### Key Findings

-   **Core Technology**: `@pocketpalai/llama.rn` (React Native bindings for llama.cpp)
-   **Architecture**: MobX-based state management with modular store pattern
-   **Model Support**: GGUF format models from Hugging Face with automatic download management
-   **Performance**: Optimized for mobile with GPU acceleration (iOS Metal), memory management, and background processing
-   **User Experience**: Seamless model switching, offline operation, and real-time streaming responses

## Technical Architecture Overview

### 1. Core Dependencies

```json
{
	"@pocketpalai/llama.rn": "0.5.8-1",
	"mobx": "^6.12.3",
	"mobx-react": "^9.1.1",
	"@dr.pogodin/react-native-fs": "^2.30.3",
	"chat-formatter": "^0.3.4"
}
```

### 2. Architecture Components

#### 2.1 Store Layer (MobX)

-   **ModelStore**: Manages model lifecycle, downloads, and LLM context
-   **ChatSessionStore**: Handles chat sessions, message history, and conversation state
-   **UIStore**: Manages application UI state and user preferences
-   **PalStore**: Manages AI personalities and system prompts

#### 2.2 Service Layer

-   **DownloadManager**: Handles model downloads with progress tracking and error handling
-   **ChatSessionRepository**: Database operations for chat persistence
-   **HuggingFace API**: Model discovery and metadata fetching

#### 2.3 Utility Layer

-   **Chat Templates**: Configurable chat formatting for different model types
-   **Completion Types**: Type-safe parameter management for LLM inference
-   **File System**: Model storage and management utilities

### 3. Model Management System

#### 3.1 Model Types and Origins

```typescript
enum ModelOrigin {
	PRESET = "preset", // Pre-configured models
	LOCAL = "local", // User-imported models
	HF = "hf", // Hugging Face models
}
```

#### 3.2 Model Storage Structure

```
DocumentDirectory/
├── models/
│   ├── preset/
│   │   └── [author]/
│   │       └── [model-file.gguf]
│   ├── hf/
│   │   └── [author]/
│   │       └── [model-file.gguf]
│   └── local/
│       └── [user-imported-models.gguf]
```

#### 3.3 Supported Model Families

-   **Gemma 2** (2B parameters, Q6_K quantization)
-   **Phi 3.5** (3.8B parameters, Q4_K_M quantization)
-   **Qwen 2.5** (1.5B-3B parameters, various quantizations)
-   **Llama 3.2** (1B-3B parameters, Q6_K/Q8_0 quantization)
-   **SmolLM2** (1.7B parameters, Q8_0 quantization)

## Detailed Implementation Plan

### Phase 1: Core Infrastructure Setup (Week 1-2)

#### 1.1 Install Core Dependencies

```bash
npm install @pocketpalai/llama.rn mobx mobx-react @dr.pogodin/react-native-fs chat-formatter
```

#### 1.2 Create Store Architecture

```typescript
// stores/ModelStore.ts
class ModelStore {
	models: Model[] = [];
	context: LlamaContext | undefined = undefined;
	activeModelId: string | undefined = undefined;
	isContextLoading: boolean = false;

	async initContext(model: Model): Promise<LlamaContext> {
		// Initialize llama.cpp context with model
	}

	async releaseContext(): Promise<void> {
		// Clean up resources
	}
}
```

#### 1.3 Set up File System Management

```typescript
// utils/modelPaths.ts
export const getModelFullPath = async (model: Model): Promise<string> => {
	const baseDir = RNFS.DocumentDirectoryPath;
	switch (model.origin) {
		case ModelOrigin.PRESET:
			return `${baseDir}/models/preset/${model.author}/${model.filename}`;
		case ModelOrigin.HF:
			return `${baseDir}/models/hf/${model.author}/${model.filename}`;
		case ModelOrigin.LOCAL:
			return model.fullPath!;
	}
};
```

### Phase 2: Model Download System (Week 3-4)

#### 2.1 Download Manager Implementation

```typescript
// services/DownloadManager.ts
export class DownloadManager {
	private downloadJobs: Map<string, DownloadJob> = new Map();

	async startDownload(
		model: Model,
		destinationPath: string,
		authToken?: string
	): Promise<void> {
		// Platform-specific download implementation
		if (Platform.OS === "ios") {
			await this.startIOSDownload(model, destinationPath, authToken);
		} else {
			await this.startAndroidDownload(model, destinationPath, authToken);
		}
	}

	private async startIOSDownload(
		model: Model,
		destinationPath: string,
		authToken?: string
	): Promise<void> {
		// Use react-native-fs for iOS downloads with background support
	}

	private async startAndroidDownload(
		model: Model,
		destinationPath: string,
		authToken?: string
	): Promise<void> {
		// Use native Android DownloadManager
	}
}
```

#### 2.2 Progress Tracking and Error Handling

-   Real-time download progress with speed and ETA calculations
-   Automatic retry mechanisms for failed downloads
-   Storage space validation before download initiation
-   Partial download cleanup on cancellation

### Phase 3: Chat System Integration (Week 5-6)

#### 3.1 Chat Template System

```typescript
// utils/chat.ts
export async function applyChatTemplate(
	messages: ChatMessage[],
	model: Model | null,
	context: LlamaContext | null
): Promise<string | JinjaFormattedChatResult> {
	// Priority: Model's custom template > Context template > Default template
	const modelChatTemplate = model?.chatTemplate;
	const contextChatTemplate =
		context?.model?.metadata?.["tokenizer.chat_template"];

	if (modelChatTemplate?.chatTemplate) {
		return applyTemplate(messages, { customTemplate: modelChatTemplate });
	} else if (contextChatTemplate) {
		return await context?.getFormattedChat(messages);
	}

	return applyTemplate(messages, { customTemplate: chatTemplates.default });
}
```

#### 3.2 Streaming Response Handler

```typescript
// hooks/useChatSession.ts
const handleSendPress = async (message: MessageType.PartialText) => {
	const context = modelStore.context;
	if (!context) return;

	// Token batching for performance
	const result = await context.completion(completionParams, (data) => {
		if (data.token) {
			queueToken(data.token, messageId, sessionId);
		}
	});

	// Process batched tokens
	await processBatchedTokens();
};
```

### Phase 4: Performance Optimization (Week 7-8)

#### 4.1 Memory Management

-   Automatic context release when app goes to background
-   Configurable GPU layer allocation for iOS Metal acceleration
-   Dynamic thread count based on device CPU cores
-   Memory usage monitoring and optimization

#### 4.2 Context Configuration

```typescript
interface ContextSettings {
	n_context: number; // Context window size
	n_batch: number; // Batch size for processing
	n_ubatch: number; // Micro-batch size
	n_threads: number; // CPU threads
	flash_attn: boolean; // Flash attention (iOS 18+)
	cache_type_k: CacheType; // Key cache type
	cache_type_v: CacheType; // Value cache type
	n_gpu_layers: number; // GPU acceleration layers
}
```

## Code Examples and Integration Points

### Model Initialization Example

```typescript
// Initialize a model for inference
const initializeModel = async (modelId: string) => {
	const model = modelStore.models.find((m) => m.id === modelId);
	if (!model || !model.isDownloaded) {
		throw new Error("Model not available");
	}

	const filePath = await getModelFullPath(model);
	const context = await initLlama({
		model: filePath,
		use_mlock: true,
		n_context: 2048,
		n_batch: 512,
		n_threads: 4,
		n_gpu_layers: Platform.OS === "ios" ? 50 : 0,
	});

	modelStore.setContext(context);
	modelStore.setActiveModel(modelId);
};
```

### Chat Completion Example

```typescript
// Send a message and get streaming response
const sendMessage = async (text: string) => {
	const context = modelStore.context;
	if (!context) return;

	const messages = [{ role: "user", content: text }];

	const prompt = await applyChatTemplate(
		messages,
		modelStore.activeModel,
		context
	);

	const response = await context.completion(
		{
			prompt,
			n_predict: 500,
			temperature: 0.7,
			stop: modelStore.activeModel?.stopWords || [],
		},
		(data) => {
			// Handle streaming tokens
			if (data.token) {
				updateMessageWithToken(data.token);
			}
		}
	);

	return response;
};
```

## Dependencies and Setup Requirements

### 1. Core Dependencies

```json
{
	"@pocketpalai/llama.rn": "0.5.8-1",
	"mobx": "^6.12.3",
	"mobx-react": "^9.1.1",
	"mobx-persist-store": "^1.1.5",
	"@dr.pogodin/react-native-fs": "^2.30.3",
	"chat-formatter": "^0.3.4",
	"@react-native-async-storage/async-storage": "^2.1.0",
	"react-native-device-info": "^14.0.4",
	"react-native-document-picker": "^9.1.2"
}
```

### 2. Platform-Specific Setup

#### iOS Configuration

```ruby
# ios/Podfile additions
pod 'RNFS', :path => '../node_modules/@dr.pogodin/react-native-fs'
```

#### Android Configuration

```gradle
// android/app/build.gradle
android {
  packagingOptions {
    pickFirst '**/libc++_shared.so'
    pickFirst '**/libjsc.so'
  }
}
```

### 3. Native Module Requirements

-   **iOS**: Metal Performance Shaders framework for GPU acceleration
-   **Android**: NDK for native library support
-   **File System**: Read/write permissions for model storage

## Migration Strategy and Timeline

### Phase 1: Foundation (Weeks 1-2)

**Deliverables:**

-   [ ] Install and configure core dependencies
-   [ ] Set up MobX store architecture
-   [ ] Implement basic model data structures
-   [ ] Create file system utilities

**Risk Level:** Low
**Dependencies:** None

### Phase 2: Model Management (Weeks 3-4)

**Deliverables:**

-   [ ] Implement DownloadManager service
-   [ ] Add model discovery from Hugging Face
-   [ ] Create model storage and validation system
-   [ ] Add progress tracking and error handling

**Risk Level:** Medium
**Dependencies:** Phase 1 completion

### Phase 3: LLM Integration (Weeks 5-6)

**Deliverables:**

-   [ ] Integrate @pocketpalai/llama.rn
-   [ ] Implement context initialization and management
-   [ ] Add chat template system
-   [ ] Create streaming response handling

**Risk Level:** High
**Dependencies:** Phase 2 completion, native module setup

### Phase 4: Chat Interface (Weeks 7-8)

**Deliverables:**

-   [ ] Build chat UI components
-   [ ] Implement message persistence
-   [ ] Add conversation management
-   [ ] Create model switching interface

**Risk Level:** Medium
**Dependencies:** Phase 3 completion

### Phase 5: Optimization & Polish (Weeks 9-10)

**Deliverables:**

-   [ ] Performance optimization
-   [ ] Memory management improvements
-   [ ] Error handling and recovery
-   [ ] User experience enhancements

**Risk Level:** Low
**Dependencies:** Phase 4 completion

## Risk Assessment and Mitigation Strategies

### High-Risk Areas

#### 1. Native Module Integration

**Risk:** Complex native dependencies may cause build issues
**Mitigation:**

-   Test on multiple device types early
-   Maintain separate development branches for iOS/Android
-   Document platform-specific build requirements
-   Create automated build verification

#### 2. Memory Management

**Risk:** Large models may cause out-of-memory crashes
**Mitigation:**

-   Implement automatic context release on background
-   Add memory usage monitoring
-   Provide user guidance on model size selection
-   Implement graceful degradation for low-memory devices

#### 3. Model Compatibility

**Risk:** Not all GGUF models may work correctly
**Mitigation:**

-   Maintain curated list of tested models
-   Implement model validation before download
-   Provide fallback to default models
-   Add model compatibility checking

### Medium-Risk Areas

#### 1. Download Reliability

**Risk:** Large model downloads may fail or corrupt
**Mitigation:**

-   Implement resume capability for interrupted downloads
-   Add file integrity verification (SHA256 hashing)
-   Provide retry mechanisms with exponential backoff
-   Cache partial downloads safely

#### 2. Performance Variability

**Risk:** Performance may vary significantly across devices
**Mitigation:**

-   Implement device capability detection
-   Provide performance benchmarking tools
-   Auto-adjust settings based on device specs
-   Offer manual performance tuning options

### Low-Risk Areas

#### 1. UI/UX Implementation

**Risk:** Interface complexity may confuse users
**Mitigation:**

-   Follow established mobile UI patterns
-   Implement progressive disclosure of advanced features
-   Provide comprehensive onboarding
-   Add contextual help and tooltips

## Performance Considerations and Optimization Recommendations

### 1. Model Selection Guidelines

#### Small Models (1-2B parameters)

-   **Use Case:** Quick responses, basic conversations
-   **Memory:** 2-4GB RAM recommended
-   **Storage:** 1-3GB per model
-   **Performance:** 5-15 tokens/second on modern devices

#### Medium Models (3-7B parameters)

-   **Use Case:** Advanced conversations, reasoning tasks
-   **Memory:** 6-8GB RAM recommended
-   **Storage:** 3-6GB per model
-   **Performance:** 2-8 tokens/second on high-end devices

### 2. Optimization Techniques

#### Memory Optimization

```typescript
// Automatic memory management
const optimizeMemoryUsage = () => {
	// Release context when app backgrounds
	AppState.addEventListener("change", (nextAppState) => {
		if (nextAppState === "background" && modelStore.useAutoRelease) {
			modelStore.releaseContext();
		}
	});

	// Monitor memory usage
	const checkMemoryUsage = () => {
		const memoryInfo = DeviceInfo.getUsedMemory();
		if (memoryInfo.used / memoryInfo.total > 0.9) {
			// Trigger memory cleanup
			modelStore.releaseContext();
		}
	};
};
```

#### Performance Tuning

```typescript
// Dynamic thread allocation based on device
const optimizeThreadCount = async () => {
	const cpuInfo = await DeviceInfo.getCPUInfo();
	const cores = cpuInfo.cores;

	// Use 80% of cores for models, reserve 20% for UI
	const optimalThreads = cores <= 4 ? cores : Math.floor(cores * 0.8);
	modelStore.setNThreads(optimalThreads);
};
```

### 3. GPU Acceleration (iOS)

```typescript
// Enable Metal acceleration on supported devices
const enableGPUAcceleration = () => {
	if (Platform.OS === "ios" && parseInt(Platform.Version) >= 18) {
		modelStore.updateUseMetal(true);
		modelStore.setNGPULayers(50); // Offload layers to GPU
	}
};
```

## Testing Strategy for Local LLM Features

### 1. Unit Testing

```typescript
// Model management tests
describe("ModelStore", () => {
	test("should initialize context correctly", async () => {
		const model = createTestModel();
		await modelStore.initContext(model);
		expect(modelStore.context).toBeDefined();
		expect(modelStore.activeModelId).toBe(model.id);
	});

	test("should handle context release", async () => {
		await modelStore.releaseContext();
		expect(modelStore.context).toBeUndefined();
	});
});
```

### 2. Integration Testing

```typescript
// End-to-end chat flow
describe("Chat Integration", () => {
	test("should complete full chat cycle", async () => {
		// Load model
		await modelStore.initContext(testModel);

		// Send message
		const response = await sendMessage("Hello, how are you?");

		// Verify response
		expect(response).toBeDefined();
		expect(response.text.length).toBeGreaterThan(0);
	});
});
```

### 3. Performance Testing

```typescript
// Benchmark model performance
describe("Performance Tests", () => {
	test("should meet minimum token generation speed", async () => {
		const startTime = Date.now();
		const response = await generateTokens(100);
		const endTime = Date.now();

		const tokensPerSecond = 100 / ((endTime - startTime) / 1000);
		expect(tokensPerSecond).toBeGreaterThan(1); // Minimum 1 token/second
	});
});
```

### 4. Device Testing Matrix

| Device Type       | RAM  | Storage | Expected Performance |
| ----------------- | ---- | ------- | -------------------- |
| iPhone 12+        | 6GB+ | 64GB+   | 8-15 tokens/sec      |
| iPhone 11         | 4GB  | 64GB+   | 5-10 tokens/sec      |
| Android High-end  | 8GB+ | 128GB+  | 6-12 tokens/sec      |
| Android Mid-range | 6GB  | 64GB+   | 3-8 tokens/sec       |

## Comparison with Current Architecture

### Current State vs. Target State

| Aspect            | Current              | Target (Post-Integration)     |
| ----------------- | -------------------- | ----------------------------- |
| AI Capabilities   | Cloud-dependent      | Offline-first with local LLMs |
| Response Time     | Network-dependent    | Consistent local processing   |
| Privacy           | Data sent to servers | Complete on-device processing |
| Offline Support   | Limited              | Full offline functionality    |
| Model Flexibility | Fixed provider       | Multiple model options        |
| Customization     | Limited              | Extensive model/prompt tuning |

### Architecture Changes Required

#### 1. State Management

-   **Current:** Basic React state or Context API
-   **Target:** MobX-based reactive state management
-   **Migration:** Gradual replacement of existing state logic

#### 2. Data Persistence

-   **Current:** AsyncStorage for simple data
-   **Target:** WatermelonDB for complex chat data + AsyncStorage for settings
-   **Migration:** Implement database migration utilities

#### 3. File Management

-   **Current:** Basic file operations
-   **Target:** Comprehensive model file management system
-   **Migration:** Add react-native-fs and implement storage utilities

## Implementation Status and Next Steps

### ✅ COMPLETED: Analysis and Planning

-   [x] Analyzed PocketPal AI codebase structure
-   [x] Identified reusable components and MIT license compatibility
-   [x] Created comprehensive integration plan
-   [x] Verified existing dependencies (@pocketpalai/llama.rn, mobx already installed)

### 🚀 READY TO IMPLEMENT: Direct Code Reuse Strategy

**Key Finding**: PocketPal AI uses MIT license, allowing direct code reuse with branding removal.

**Files Ready for Direct Copy (with branding removal):**

1. `pocketpal-ai/src/services/downloads/` → `src/services/downloads/` (Complete download system)
2. `pocketpal-ai/src/utils/chat.ts` → `src/utils/chat.ts` (Chat templates & formatting)
3. `pocketpal-ai/src/utils/completionTypes.ts` → `src/utils/completionTypes.ts` (Type definitions)
4. `pocketpal-ai/src/store/defaultModels.ts` → `src/store/defaultModels.ts` (Model configurations)
5. `pocketpal-ai/src/api/hf.ts` → `src/api/hf.ts` (HuggingFace API integration)

### Priority 1 (Critical - Start Immediately)

1. **Copy Download Management System**

    - Copy complete `services/downloads/` folder from PocketPal AI
    - Remove PocketPal branding and adapt imports
    - This provides production-ready iOS/Android download handling

2. **Enhance Existing ModelStore**
    - Merge PocketPal's advanced ModelStore features with existing implementation
    - Add download manager integration
    - Implement context management and GPU acceleration

### Priority 2 (High - Week 2-3)

1. **Implement model management system**

    - Create DownloadManager service
    - Add model discovery and download capabilities
    - Implement file system organization

2. **Build chat interface foundation**
    - Create basic chat UI components
    - Implement message persistence
    - Add streaming response handling

### Priority 3 (Medium - Week 4-6)

1. **Add advanced features**

    - Implement multiple model support
    - Add chat template system
    - Create model switching interface

2. **Performance optimization**
    - Add GPU acceleration for iOS
    - Implement memory management strategies
    - Optimize for different device capabilities

### Priority 4 (Low - Week 7-10)

1. **Polish and enhancement**

    - Add comprehensive error handling
    - Implement user onboarding
    - Create performance monitoring tools

2. **Testing and validation**
    - Comprehensive device testing
    - Performance benchmarking
    - User acceptance testing

## Conclusion

The integration of local LLM capabilities represents a significant enhancement to our application, providing users with powerful AI features while maintaining privacy and offline functionality. The PocketPal AI codebase demonstrates a mature, production-ready approach that can be adapted to our needs.

The proposed implementation leverages proven technologies and patterns, with a clear migration path and risk mitigation strategies. By following the phased approach outlined in this document, we can deliver a robust local LLM integration that enhances user experience while maintaining application stability and performance.

**Estimated Timeline:** 10 weeks for full implementation
**Resource Requirements:** 2-3 developers with React Native and native development experience
**Success Metrics:**

-   Successful model loading and inference on target devices
-   Response times under 2 seconds for typical queries
-   Memory usage within acceptable limits (< 80% of available RAM)
-   User satisfaction with offline AI capabilities
