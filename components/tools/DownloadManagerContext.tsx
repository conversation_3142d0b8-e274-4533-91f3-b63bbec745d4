import React, { createContext, useState, useCallback, useContext, useRef, ReactNode } from 'react';
import * as FileSystem from 'expo-file-system';

// Define download states
type DownloadState = 'idle' | 'downloading' | 'paused' | 'completed' | 'failed';

// Download Task Context
type DownloadManagerContextType = {
  downloadState: DownloadState;
  downloadProgress: number;
  startDownload: (url: string, fileUri: string) => void;
  pauseDownload: () => void;
  resumeDownload: () => void;
  cancelDownload: () => void;
};

// Define the prop types for the DownloadManagerProvider component, including children
type DownloadManagerProviderProps = {
  children: ReactNode;
};

const DownloadManagerContext = createContext<DownloadManagerContextType | undefined>(undefined);

export const useDownloadManager = () => {
  const context = useContext(DownloadManagerContext);
  if (!context) {
    throw new Error('useDownloadManager must be used within a DownloadManagerProvider');
  }
  return context;
};

export const DownloadManagerProvider: React.FC<DownloadManagerProviderProps> = ({ children }) => {
  const [downloadState, setDownloadState] = useState<DownloadState>('idle');
  const [downloadProgress, setDownloadProgress] = useState(0);
  const downloadResumable = useRef<FileSystem.DownloadResumable | null>(null);

  // Start a download
  const startDownload = useCallback(async (url: string, fileUri: string) => {
    setDownloadState('downloading');
    const download = FileSystem.createDownloadResumable(
      url,
      fileUri,
      {},
      (downloadProgress) => {
        setDownloadProgress(downloadProgress.totalBytesWritten / downloadProgress.totalBytesExpectedToWrite);
      },
    );
    downloadResumable.current = download;

    try {
      await download.downloadAsync();
      setDownloadState('completed');
    } catch (error) {
      setDownloadState('failed');
    }
  }, []);

  // Pause a download
  const pauseDownload = useCallback(() => {
    if (downloadResumable.current) {
      downloadResumable.current.pauseAsync();
      setDownloadState('paused');
    }
  }, []);

  // Resume a download
  const resumeDownload = useCallback(() => {
    if (downloadResumable.current) {
      downloadResumable.current.resumeAsync();
      setDownloadState('downloading');
    }
  }, []);

  // Cancel a download
  const cancelDownload = useCallback(async () =>  {
    if (downloadResumable.current) {
      await downloadResumable.current.cancelAsync();
      setDownloadState('idle');
      setDownloadProgress(0);
    }
  }, []);

  return (
    <DownloadManagerContext.Provider
      value={{
        downloadState,
        downloadProgress,
        startDownload,
        pauseDownload,
        resumeDownload,
        cancelDownload,
      }}
    >
      {children}
    </DownloadManagerContext.Provider>
  );
};