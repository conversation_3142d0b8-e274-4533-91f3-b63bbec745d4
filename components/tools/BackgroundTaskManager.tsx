import * as TaskManager from 'expo-task-manager';
import * as FileSystem from 'expo-file-system';

// Define the task name for the background download task
const DOWNLOAD_TASK_NAME = 'background-download-task';

// Define the task executor function, which will be triggered by the background task system
TaskManager.defineTask(DOWNLOAD_TASK_NAME, async ({ data, error, executionInfo }: any) => {
  if (error) {
    console.error('Download failed', error);
    return Promise.reject(error); // Reject if there's an error
  }

  if (data) {
    const { fileUri, progress } = data;
    console.log(`Downloaded file to: ${fileUri}, Progress: ${progress}%`);
    return Promise.resolve(); // Resolve when the task completes
  }

  return Promise.resolve(); // Default resolve for success
});

// Function to start a background download task
export const startBackgroundDownload = async (url: string, fileUri: string) => {
  const downloadResumable = FileSystem.createDownloadResumable(url, fileUri, {});

  try {
    await downloadResumable.downloadAsync();
    // Once the download completes, we trigger the background task
    const eventId = Date.now().toString(); // Create a unique event ID
    const taskData = { fileUri, progress: 100 }; // Task data to be passed to the task executor
    const executionInfo = { taskName: DOWNLOAD_TASK_NAME, eventId };

    // Use the registered task executor with the correct body format
    await TaskManager.isTaskRegisteredAsync(DOWNLOAD_TASK_NAME)
      .then(async (isRegistered) => {
        if (isRegistered) {
          // Trigger the task executor with the task data and execution info
          await TaskManager.getTaskOptionsAsync(DOWNLOAD_TASK_NAME).then(async () => {
            // Pass the data and execution info to the task
            await TaskManager.defineTask(DOWNLOAD_TASK_NAME, {
              data: taskData,
              error: null,
              executionInfo,
            });
          });
        }
      });
  } catch (error) {
    console.error('Download failed', error);
  }
};