import React, { useState, useRef } from "react";
import {
	View,
	TextInput,
	Button,
	FlatList,
	Text,
	StyleSheet,
	TouchableWithoutFeedback,
	Keyboard,
} from "react-native";
import OpenAI from "openai";

const apiKey = process.env.EXPO_PUBLIC_OPENAI_API_KEY; // Fallback to a default API key
if (!apiKey) {
	throw new Error(
		"The OPENAI_API_KEY environment variable is missing or empty."
	);
}

const client = new OpenAI({
	apiKey, // Use environment variable for OpenAI API key
	dangerouslyAllowBrowser: true, // Enable for development only
});

export function Chatbot() {
	const [messages, setMessages] = useState<
		{ text: string; sender: "user" | "bot"; time: string }[]
	>([]);
	const [input, setInput] = useState("");
	const inputRef = useRef<TextInput>(null);

	const handleSend = async () => {
		if (!input.trim()) return;

		const userMessage = {
			text: input,
			sender: "user" as const,
			time: new Date().toLocaleTimeString(),
		};
		setMessages((prev) => [...prev, userMessage]);
		setInput("");

		const contextMessages = messages.slice(0, 9).map((msg) => ({
			role: msg.sender === "user" ? "user" : "assistant",
			content: msg.text,
		}));

		try {
			try {
				const stream = await client.chat.completions.create({
					model: "gpt-4o-mini",
					messages: [...contextMessages, { role: "user", content: input }],
					stream: true,
				});

				const botMessage = {
					text: "",
					sender: "bot" as const,
					time: new Date().toLocaleTimeString(),
				};
				setMessages((prev) => [...prev, botMessage]);

				let hasValidContent = false; // Flag to track if any valid content is received
				for await (const chunk of stream) {
					if (!chunk.choices || chunk.choices.length === 0) {
						console.error("Received an empty response body.");
						break; // Exit the loop if there's no valid content
					}
					const text = chunk.choices[0]?.delta?.content || "";
					if (text) {
						hasValidContent = true;
						setMessages((prev) => {
							const updatedMessages = [...prev];
							updatedMessages[updatedMessages.length - 1].text +=
								text;
							return updatedMessages;
						});
					}
				}

				// If no valid content received, handle the case
				if (!hasValidContent) {
					setMessages((prev) => [
						...prev,
						{
							text: "No response received.",
							sender: "bot",
							time: new Date().toLocaleTimeString(),
						},
					]);
				}
			} catch (error) {
				console.error("Error while streaming:", error);
				setMessages((prev) => [
					...prev,
					{
						text: "Error retrieving response.",
						sender: "bot",
						time: new Date().toLocaleTimeString(),
					},
				]);
			}
		} catch (error) {
			console.error("Error while streaming:", error);
			setMessages((prev) => [
				...prev,
				{
					text: "Error retrieving response.",
					sender: "bot",
					time: new Date().toLocaleTimeString(),
				},
			]);
		}
	};

	const handleKeyPress = (event: React.KeyboardEvent) => {
		if (event.key === "Enter") {
			handleSend();
		}
	};

	return (
			<View style={styles.container}>
				<FlatList
					data={messages}
					renderItem={({ item }) => (
						<View>
							<Text
								style={
									item.sender === "user"
										? styles.userMessage
										: styles.botMessage
								}
							>
								{item.text}
							</Text>
							<Text style={styles.timestamp}>
								{item.time}
							</Text>
						</View>
					)}
					keyExtractor={(item, index) => index.toString()}
					inverted // To ensure the latest message is displayed at the bottom
					contentContainerStyle={styles.scrollContainer}
				/>

				<View style={styles.inputContainer}>
					<TextInput
						ref={inputRef}
						style={styles.input}
						value={input}
						onChangeText={setInput}
						placeholder="Type your message..."
						onKeyPress={handleKeyPress}
						editable
						onSubmitEditing={handleSend} // Ensure Enter key works on mobile
					/>
					<Button title="Send" onPress={handleSend} />
				</View>
			</View>
	);
}

const styles = StyleSheet.create({
	container: {
		flex: 1,
		padding: 16,
		backgroundColor: "#fff",
	},
	scrollContainer: {
		flexGrow: 1, // To allow scrolling when content is large
		paddingBottom: 20, // Space at the bottom for the input
	},
	inputContainer: {
		flexDirection: "row",
		alignItems: "center",
		justifyContent: "space-between",
		marginBottom: 16,
	},
	input: {
		borderWidth: 1,
		borderColor: "#ccc",
		borderRadius: 4,
		padding: 8,
		flex: 1,
		marginRight: 8,
	},
	userMessage: {
		alignSelf: "flex-end",
		backgroundColor: "#d1e7dd",
		padding: 8,
		borderRadius: 4,
		marginVertical: 4,
	},
	botMessage: {
		alignSelf: "flex-start",
		backgroundColor: "#f8d7da",
		padding: 8,
		borderRadius: 4,
		marginVertical: 4,
	},
	timestamp: {
		fontSize: 10,
		color: "#888",
		alignSelf: "flex-end",
	},
});
