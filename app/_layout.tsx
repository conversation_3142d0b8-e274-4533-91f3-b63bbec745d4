import {
	<PERSON><PERSON>hem<PERSON>,
	De<PERSON><PERSON><PERSON>heme,
	Theme<PERSON><PERSON><PERSON>,
} from "@react-navigation/native";
import { useFonts } from "expo-font";
import { Stack } from "expo-router";
import * as SplashScreen from "expo-splash-screen";
import { StatusBar } from "expo-status-bar";
import { useEffect } from "react";
import "react-native-reanimated";

import { getApp, initializeApp } from "@react-native-firebase/app";

import { getAnalytics } from "@react-native-firebase/analytics";
import { getAuth } from "@react-native-firebase/auth";
import { getFirestore } from "@react-native-firebase/firestore";
import { getMessaging } from "@react-native-firebase/messaging";
import { getRemoteConfig } from "@react-native-firebase/remote-config";
import { getStorage } from "@react-native-firebase/storage";
import { Platform } from "react-native";

import { useColorScheme } from "@/hooks/useColorScheme";
import * as Sentry from '@sentry/react-native';

Sentry.init({
  dsn: 'https://<EMAIL>/4508593927815168',

  // uncomment the line below to enable Spotlight (https://spotlightjs.com)
  // enableSpotlight: __DEV__,
});

// web requires dynamic initialization on web prior to using firebase
if (Platform.OS === "web") {
	// https://firebase.google.com/docs/web/setup#available-libraries

	// Your web app's Firebase configuration
	// For Firebase JS SDK v7.20.0 and later, measurementId is optional
	const firebaseConfig = {
		apiKey: "AIzaSyBrgk7mYiKlII2nklq-6I7ji8yxX2v8Vsg",
		authDomain: "diogenesaicompanion.firebaseapp.com",
		projectId: "diogenesaicompanion",
		databaseURL: "https://diogenesaicompanion-default-rtdb.firebaseio.com/",
		storageBucket: "diogenesaicompanion.firebasestorage.app",
		messagingSenderId: "367623671079",
		appId: "1:367623671079:web:0c554c48397671ba592d02",
		measurementId: "G-ECCMN91SRJ",
	};

	// Initialize Firebase
	initializeApp(firebaseConfig);
}

// // ...now throughout your app, use firebase APIs normally, for example:
// const firebaseApp = getApp();
// const analytics = getAnalytics(firebaseApp);
// const messaging = getMessaging(firebaseApp);

// // Initialize Cloud Firestore and get a reference to the service
// const db = getFirestore(firebaseApp);
// // Initialize Firebase Authentication and get a reference to the service
// const auth = getAuth(firebaseApp);
// // Initialize Remote Config and get a reference to the service
// const remoteConfig = getRemoteConfig(firebaseApp);
// // Initialize Cloud Storage and get a reference to the service
// const storage = getStorage(firebaseApp);

// Prevent the splash screen from auto-hiding before asset loading is complete.
SplashScreen.preventAutoHideAsync();

export default function RootLayout() {
	const colorScheme = useColorScheme();
	const [loaded] = useFonts({
		SpaceMono: require("../assets/fonts/SpaceMono-Regular.ttf"),
	});

	useEffect(() => {
		if (loaded) {
			SplashScreen.hideAsync();
		}
	}, [loaded]);

	if (!loaded) {
		return null;
	}

	return (
		<ThemeProvider
			value={colorScheme === "dark" ? DarkTheme : DefaultTheme}
		>
			<Stack>
				<Stack.Screen name="(tabs)" options={{ headerShown: false }} />
				<Stack.Screen name="+not-found" />
			</Stack>
			<StatusBar style="auto" />
		</ThemeProvider>
	);
}
