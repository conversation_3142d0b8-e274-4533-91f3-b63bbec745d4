import React, { useState, useEffect } from "react";
import { View, Text, TextInput, Button, StyleSheet } from "react-native";
import auth, { FirebaseAuthTypes } from "@react-native-firebase/auth";
import Toast from "react-native-toast-message"; // Install this package
import { Link, useRouter } from "expo-router";
import { ThemedText } from "@/components/ThemedText";

const Auth = () => {
	const [email, setEmail] = useState("");
	const [password, setPassword] = useState("");
	const [isSignUp, setIsSignUp] = useState(false);
	const [initializing, setInitializing] = useState(true);
	const [user, setUser] = useState();
	const router = useRouter();

	function onAuthStateChanged(user: FirebaseAuthTypes.User | null | boolean) {
		setUser(user);
		if (initializing) setInitializing(false);
		if (user) {
			router.replace("/");
			// Redirect to index after login
		}
	}

	useEffect(() => {
		const subscriber = auth().onAuthStateChanged(onAuthStateChanged);
		return subscriber; // unsubscribe on unmount
	}, []);

	const validateEmail = (email) => {
		const re = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/;
		return re.test(email);
	};

	const validatePassword = (password) => {
		const re = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{6,}$/;
		return re.test(password);
	};

	const handleSignUp = async () => {
		if (!validateEmail(email)) {
			Toast.show({ text1: "Invalid email format", type: "error" });
			return;
		}
		if (!validatePassword(password)) {
			Toast.show({
				text1: "Password must be at least 6 characters and contain both letters and numbers",
				type: "error",
			});
			return;
		}

		try {
			const response = await auth().createUserWithEmailAndPassword(
				email,
				password
			);
			Toast.show({
				text1: "Please check your email inbox and verify your email",
				type: "info",
			});
			if (response.user.emailVerified) {
				setEmail("");
				setPassword("");
			} else {
				Toast.show({
					text1: "Please verify your email first",
					type: "info",
				});
				sendVerificationEmail();
			}
		} catch (error) {
			handleAuthError(error);
		}
	};

	const handleSignIn = async () => {
		if (!validateEmail(email)) {
			Toast.show({ text1: "Invalid email format", type: "error" });
			return;
		}
		if (!validatePassword(password)) {
			Toast.show({
				text1: "Password must be at least 6 characters and contain both letters and numbers",
				type: "error",
			});
			return;
		}

		try {
			const response = await auth().signInWithEmailAndPassword(
				email,
				password
			);
			if (response.user.emailVerified) {
				setEmail("");
				setPassword("");
			} else {
				Toast.show({
					text1: "Please verify your email first",
					type: "info",
				});
				sendVerificationEmail();
			}
		} catch (error) {
			handleAuthError(error);
		}
	};

	const sendVerificationEmail = async () => {
		try {
			const user = auth().currentUser;
			if (user) {
				await user.sendEmailVerification();
				Toast.show({
					text1: "Verification email sent. Please check your inbox.",
					type: "success",
				});
			}
		} catch (error) {
			console.error(error);
			Toast.show({
				text1: "Error sending verification email. Please try again.",
				type: "error",
			});
		}
	};

	const handleAuthError = (error) => {
		switch (error.code) {
			case "auth/email-already-in-use":
				Toast.show({ text1: "Email is already in use", type: "error" });
				break;
			case "auth/invalid-email":
				Toast.show({ text1: "Invalid email address", type: "error" });
				break;
			case "auth/user-disabled":
				Toast.show({
					text1: "User account has been disabled",
					type: "error",
				});
				break;
			case "auth/wrong-password":
				Toast.show({ text1: "Wrong password", type: "error" });
				break;
			default:
				Toast.show({
					text1: "An error occurred. Please try again.",
					type: "error",
				});
		}
	};

	const handleResetPassword = async () => {
		if (!validateEmail(email)) {
			Toast.show({ text1: "Invalid email format", type: "error" });
			return;
		}

		try {
			await auth().sendPasswordResetEmail(email);
			Toast.show({
				text1: "Password reset email sent!",
				type: "success",
			});
		} catch (error) {
			handleAuthError(error);
		}
	};

	if (initializing) return null;

	if (!user) {
		return (
			<View style={styles.container}>
				<Text>{isSignUp ? "Sign Up" : "Login"}</Text>
				<TextInput
					placeholder="Email"
					value={email}
					onChangeText={setEmail}
					style={styles.input}
				/>
				<TextInput
					placeholder="Password"
					value={password}
					onChangeText={setPassword}
					secureTextEntry
					style={styles.input}
				/>
				<Button
					title={isSignUp ? "Sign Up" : "Login"}
					onPress={isSignUp ? handleSignUp : handleSignIn}
				/>
				<Button
					title={isSignUp ? "Switch to Login" : "Switch to Sign Up"}
					onPress={() => setIsSignUp(!isSignUp)}
				/>
				<Button title="Reset Password" onPress={handleResetPassword} />
			</View>
		);
	}

	return (
		<View style={styles.container}>
			<Text>Welcome {user.email}</Text>
			<Button title="Logout" onPress={() => auth().signOut()} />
		</View>
	);
};

const styles = StyleSheet.create({
	container: {
		flex: 1,
		justifyContent: "center",
		padding: 16,
	},
	input: {
		borderWidth: 1,
		borderColor: "#ccc",
		borderRadius: 4,
		padding: 8,
		marginBottom: 16,
	},
});

export default Auth;
