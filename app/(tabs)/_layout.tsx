import { Tabs } from "expo-router";
import React from "react";
import { Platform } from "react-native";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import { Drawer } from "expo-router/drawer";

import { HapticTab } from "@/components/HapticTab";
import { IconSymbol } from "@/components/ui/IconSymbol";
import TabBarBackground from "@/components/ui/TabBarBackground";
import { Colors } from "@/constants/Colors";
import { useColorScheme } from "@/hooks/useColorScheme";
// import StreamingChat from "./streamingChat";

export default function TabLayout() {
	const colorScheme = useColorScheme();

	return (
		<GestureHandlerRootView style={{ flex: 1 }}>
			<Drawer>
				<Tabs
					screenOptions={{
						tabBarActiveTintColor:
							Colors[colorScheme ?? "light"].tint,
						headerShown: false,
						tabBarButton: HapticTab,
						tabBarBackground: TabBarBackground,
						tabBarStyle: Platform.select({
							ios: {
								position: "absolute",
							},
							default: {},
						}),
					}}
				>
					<Tabs.Screen
						name="index"
						options={{
							title: "Home",
						}}
					></Tabs.Screen>
					<Tabs.Screen
						name="main"
						options={{
							title: "Main",
						}}
					></Tabs.Screen>
					{/*<Tabs.Screen*/}
					{/*	name="chatbot"*/}
					{/*	options={{*/}
					{/*		title: "Chatbot",*/}
					{/*		tabBarIcon: ({ color }) => (*/}
					{/*			<IconSymbol*/}
					{/*				size={28}*/}
					{/*				name="chevron.right"*/}
					{/*				color={color}*/}
					{/*			/>*/}
					{/*		),*/}
					{/*	}}*/}
					{/*></Tabs.Screen>*/}
					{/*{(Platform.OS === "ios" || Platform.OS === "android") && (*/}
					{/*	<Tabs.Screen*/}
					{/*		name="llamacppChat"*/}
					{/*		options={{*/}
					{/*			title: "Local llm",*/}
					{/*		}}*/}
					{/*	/>*/}
					{/*)}*/}
					{Platform.OS === "ios" && (
						<Tabs.Screen
							name="stablediffusion"
							options={{
								title: "Stable Diffusion",
							}}
						/>
					)}
					{/* <Tabs.Screen
						name="streamingChat"
						options={{
							title: "Local llm",
						}}
					>
					</Tabs.Screen> */}
				</Tabs>
			</Drawer>
		</GestureHandlerRootView>
	);
}
