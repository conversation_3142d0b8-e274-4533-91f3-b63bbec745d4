import React, { useEffect, useState } from "react";
import { View, Text, Button, StyleSheet } from "react-native";
import auth from "@react-native-firebase/auth";
import { useRouter } from "expo-router";

const Home = () => {
	const router = useRouter();
	const [user, setUser] = useState<any>(null); // Track user state
	const [isInitialized, setIsInitialized] = useState(false);

	// Listen for auth state changes
	useEffect(() => {
		const unsubscribe = auth().onAuthStateChanged((user) => {
			setUser(user);
			setIsInitialized(true);
		});
		return () => unsubscribe(); // Cleanup the listener
	}, []);

	useEffect(() => {
		if (isInitialized && !user) {
			router.replace("/Auth");
		}
	}, [isInitialized, user]);

	if (!isInitialized) {
		return <Text>Loading...</Text>; // Show loading while initializing
	}

	if (user == null) {
		return (
			<View style={styles.container}>
				<Text> Please log in first</Text>
			</View>
		);
	}

	return (
		<View style={styles.container}>
			<Text>Welcome {user?.email}</Text>
			<Button
				title="Logout"
				onPress={async () => {
					await auth().signOut();
					router.replace("/Auth");
				}}
			/>
		</View>
	);
};

const styles = StyleSheet.create({
	container: {
		flex: 1,
		justifyContent: "center",
		alignItems: "center",
	},
});

export default Home;