import React from "react";
import { View, StyleSheet, FlatList } from 'react-native';
import { Chatbot } from "@/components/Chatbot";
import { ThemedView } from "@/components/ThemedView";

export default function ChatbotScreen() {
  return (
    <ThemedView style={styles.container}>
      <FlatList
        data={[{}]} // Placeholder data to render the FlatList
        renderItem={() => (
          <View style={styles.chatbotContainer}>
            <Chatbot />
          </View>
        )}
        keyExtractor={(item, index) => index.toString()}
        contentContainerStyle={styles.flatListContainer}
      />
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  chatbotContainer: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  flatListContainer: {
    flexGrow: 1,
  },
});
