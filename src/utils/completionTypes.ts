export interface CompletionParams {
  prompt?: string;
  n_predict?: number;
  temperature?: number;
  top_k?: number;
  top_p?: number;
  min_p?: number;
  tfs_z?: number;
  typical_p?: number;
  penalty_last_n?: number;
  penalty_repeat?: number;
  penalty_freq?: number;
  penalty_present?: number;
  mirostat?: number;
  mirostat_tau?: number;
  mirostat_eta?: number;
  penalize_nl?: boolean;
  seed?: number;
  n_probs?: number;
  stop?: string[];
  ignore_eos?: boolean;
  logit_bias?: Array<[number, number]>;
  n_threads?: number;
  grammar?: string;
}

export interface CompletionResult {
  text: string;
  completion_probabilities?: Array<{
    content: string;
    probs: Array<{
      prob: number;
      tok_str: string;
    }>;
  }>;
  timings?: {
    predicted_ms: number;
    predicted_n: number;
    predicted_per_token_ms: number;
    predicted_per_second: number;
    prompt_ms: number;
    prompt_n: number;
    prompt_per_token_ms: number;
    prompt_per_second: number;
  };
}

export interface TokenData {
  token: string;
  completion_probabilities?: Array<{
    content: string;
    probs: Array<{
      prob: number;
      tok_str: string;
    }>;
  }>;
}

export interface CompletionTokenCallback {
  (data: TokenData): void;
}
