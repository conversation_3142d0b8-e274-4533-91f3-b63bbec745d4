import { ChatTemplateConfig, ChatMessage, Model, CompletionParams } from '../types/llm';
import { LlamaContext } from '@pocketpalai/llama.rn';

// Default completion parameters
export const defaultCompletionParams: CompletionParams = {
  n_predict: 500,
  temperature: 0.7,
  top_k: 40,
  top_p: 0.9,
  min_p: 0.05,
  penalty_repeat: 1.1,
  penalty_freq: 0.0,
  penalty_present: 0.0,
  mirostat: 0,
  mirostat_tau: 5.0,
  mirostat_eta: 0.1,
  seed: -1,
  stop: [],
  version: 1,
  include_thinking_in_context: true,
};

// Pre-defined chat templates for different model families
export const chatTemplates: Record<string, ChatTemplateConfig> = {
  default: {
    name: 'default',
    addGenerationPrompt: true,
    bosToken: '',
    eosToken: '',
    chatTemplate: '',
    systemPrompt: 'You are a helpful conversational chat assistant. You are precise, concise, and casual.',
    addBosToken: false,
    addEosToken: false,
  },
  
  gemma: {
    name: 'gemma',
    addGenerationPrompt: true,
    bosToken: '<bos>',
    eosToken: '<eos>',
    chatTemplate: '',
    systemPrompt: 'You are a helpful assistant named Gemma. You are precise, concise, and casual.',
    addBosToken: false,
    addEosToken: false,
  },
  
  phi3: {
    name: 'phi3',
    addGenerationPrompt: true,
    bosToken: '<|im_start|>',
    eosToken: '<|im_end|>',
    chatTemplate: '',
    systemPrompt: 'You are a helpful conversational chat assistant. You are precise, concise, and casual.',
    addBosToken: false,
    addEosToken: false,
  },
  
  qwen: {
    name: 'qwen',
    addGenerationPrompt: true,
    bosToken: '<|im_start|>',
    eosToken: '<|im_end|>',
    chatTemplate: '',
    systemPrompt: 'You are Qwen, created by Alibaba Cloud. You are a helpful assistant.',
    addBosToken: false,
    addEosToken: false,
  },
  
  llama: {
    name: 'llama',
    addGenerationPrompt: true,
    bosToken: '<|begin_of_text|>',
    eosToken: '<|end_of_text|>',
    chatTemplate: '',
    systemPrompt: 'You are a helpful conversational chat assistant. You are precise, concise, and casual.',
    addBosToken: false,
    addEosToken: false,
  },
  
  smolLM: {
    name: 'smolLM',
    addGenerationPrompt: true,
    bosToken: '<|im_start|>',
    eosToken: '<|im_end|>',
    chatTemplate: '',
    systemPrompt: 'You are a helpful assistant.',
    addBosToken: false,
    addEosToken: false,
  },
  
  custom: {
    name: 'custom',
    addGenerationPrompt: true,
    bosToken: '',
    eosToken: '',
    chatTemplate: '',
    systemPrompt: '',
    addBosToken: false,
    addEosToken: false,
  },
};

/**
 * Convert messages to a simple chat format
 */
export const convertToChatMessages = (messages: any[]): ChatMessage[] => {
  return messages
    .filter(message => message.type === 'text' && message.text !== undefined)
    .map(message => ({
      content: message.text,
      role: message.author.id === 'assistant' ? 'assistant' : 'user',
    }))
    .reverse(); // Reverse to get chronological order
};

/**
 * Apply chat template to format messages for the model
 * This is a simplified version - in production you'd use chat-formatter library
 */
export const applyChatTemplate = async (
  messages: ChatMessage[],
  model: Model | null,
  context: LlamaContext | null,
): Promise<string> => {
  try {
    // Priority: Model's custom template > Context template > Default template
    const modelChatTemplate = model?.chatTemplate;
    
    // If model has a custom chat template, use it
    if (modelChatTemplate?.chatTemplate && modelChatTemplate.chatTemplate.trim()) {
      // TODO: Implement custom template formatting
      // For now, use simple formatting
      return formatMessagesSimple(messages, modelChatTemplate);
    }
    
    // Try to use context's built-in chat template
    if (context) {
      try {
        const formattedChat = await context.getFormattedChat(messages);
        if (formattedChat && typeof formattedChat === 'string') {
          return formattedChat;
        }
      } catch (error) {
        console.warn('Failed to use context chat template:', error);
      }
    }
    
    // Fallback to default template
    const defaultTemplate = model?.chatTemplate || chatTemplates.default;
    return formatMessagesSimple(messages, defaultTemplate);
    
  } catch (error) {
    console.error('Error applying chat template:', error);
    // Ultimate fallback - just concatenate messages
    return messages.map(msg => `${msg.role}: ${msg.content}`).join('\n') + '\nassistant:';
  }
};

/**
 * Simple message formatting for basic chat templates
 */
const formatMessagesSimple = (messages: ChatMessage[], template: ChatTemplateConfig): string => {
  let formatted = '';
  
  // Add BOS token if needed
  if (template.addBosToken && template.bosToken) {
    formatted += template.bosToken;
  }
  
  // Add system message if available
  const systemMessages = messages.filter(msg => msg.role === 'system');
  const otherMessages = messages.filter(msg => msg.role !== 'system');
  
  if (systemMessages.length > 0) {
    formatted += `system: ${systemMessages[0].content}\n`;
  } else if (template.systemPrompt) {
    formatted += `system: ${template.systemPrompt}\n`;
  }
  
  // Add conversation messages
  for (const message of otherMessages) {
    formatted += `${message.role}: ${message.content}\n`;
  }
  
  // Add generation prompt
  if (template.addGenerationPrompt) {
    formatted += 'assistant:';
  }
  
  return formatted;
};

/**
 * Get default settings for local models
 */
export const getLocalModelDefaultSettings = (): {
  chatTemplate: ChatTemplateConfig;
  completionParams: CompletionParams;
} => {
  return {
    chatTemplate: { ...chatTemplates.custom },
    completionParams: { ...defaultCompletionParams },
  };
};

/**
 * Get default settings for Hugging Face models
 */
export const getHFDefaultSettings = (): {
  chatTemplate: ChatTemplateConfig;
  completionParams: CompletionParams;
} => {
  return {
    chatTemplate: { ...chatTemplates.default },
    completionParams: { ...defaultCompletionParams },
  };
};

/**
 * Remove thinking parts from text content
 */
export const removeThinkingParts = (text: string): string => {
  // Check if the text contains any thinking tags
  const hasThinkingTags =
    text.includes('<think>') ||
    text.includes('<thought>') ||
    text.includes('<thinking>');

  // If no thinking tags are found, return the original text
  if (!hasThinkingTags) {
    return text;
  }

  // Remove content between thinking tags
  let result = text.replace(/<think>[\s\S]*?<\/think>/g, '');
  result = result.replace(/<thought>[\s\S]*?<\/thought>/g, '');
  result = result.replace(/<thinking>[\s\S]*?<\/thinking>/g, '');

  console.log('Removed thinking parts from context');
  return result;
};

/**
 * Common stop tokens used by various models
 */
export const commonStopTokens = [
  '</s>',
  '<|end|>',
  '<|eot_id|>',
  '<|end_of_text|>',
  '<|im_end|>',
  '<|EOT|>',
  '<|END_OF_TURN_TOKEN|>',
  '<|end_of_turn|>',
  '<end_of_turn>',
  '<|endoftext|>',
];
