import {CompletionParams} from './completionTypes';

export const defaultCompletionParams: CompletionParams = {
  n_predict: 500,
  temperature: 0.7,
  top_k: 40,
  top_p: 0.95,
  min_p: 0.05,
  tfs_z: 1.0,
  typical_p: 1.0,
  penalty_last_n: 64,
  penalty_repeat: 1.1,
  penalty_freq: 0.0,
  penalty_present: 0.0,
  mirostat: 0,
  mirostat_tau: 5.0,
  mirostat_eta: 0.1,
  penalize_nl: true,
  seed: -1,
  n_probs: 0,
  stop: [],
  ignore_eos: false,
  logit_bias: [],
  n_threads: 4,
  grammar: '',
};
