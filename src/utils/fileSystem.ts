import * as RNFS from '@dr.pogodin/react-native-fs';
import { Model, ModelOrigin } from '../types/llm';

/**
 * Determines the full path for a model file on the device's storage.
 * This path is used for multiple purposes:
 * - As the destination path when downloading a model
 * - To check if a model is downloaded (by checking file existence at this path)
 * - To access the model file for operations like context initialization or deletion
 *
 * Path structure varies by model origin:
 * - LOCAL: Uses the model's fullPath property
 * - PRESET: Uses DocumentDirectoryPath/models/preset/author/filename
 * - HF: Uses DocumentDirectoryPath/models/hf/author/filename
 */
export const getModelFullPath = async (model: Model): Promise<string> => {
  // For local models, use the fullPath
  if (model.isLocal || model.origin === ModelOrigin.LOCAL) {
    if (!model.fullPath) {
      throw new Error('Full path is undefined for local model');
    }
    return model.fullPath;
  }

  if (!model.filename) {
    throw new Error('Model filename is undefined');
  }

  const baseDir = RNFS.DocumentDirectoryPath;
  const author = model.author || 'unknown';

  // For preset models
  if (model.origin === ModelOrigin.PRESET) {
    const oldPath = `${baseDir}/${model.filename}`; // Legacy path for backwards compatibility
    const newPath = `${baseDir}/models/preset/${author}/${model.filename}`;

    // Check if file exists in old path first (backwards compatibility)
    try {
      if (await RNFS.exists(oldPath)) {
        return oldPath;
      }
    } catch (err) {
      console.log('Error checking old path:', err);
    }

    return newPath;
  }

  // For HF models
  if (model.origin === ModelOrigin.HF) {
    return `${baseDir}/models/hf/${author}/${model.filename}`;
  }

  // Fallback (shouldn't reach here)
  console.error('Unexpected model origin:', model.origin);
  return `${baseDir}/${model.filename}`;
};

/**
 * Check if a model file exists on the device
 */
export const checkModelFileExists = async (model: Model): Promise<boolean> => {
  try {
    const filePath = await getModelFullPath(model);
    return await RNFS.exists(filePath);
  } catch (error) {
    console.error('Error checking model file existence:', error);
    return false;
  }
};

/**
 * Create directory structure for model storage
 */
export const createModelDirectory = async (model: Model): Promise<void> => {
  try {
    const filePath = await getModelFullPath(model);
    const dirPath = filePath.substring(0, filePath.lastIndexOf('/'));
    
    // Create directory if it doesn't exist
    const dirExists = await RNFS.exists(dirPath);
    if (!dirExists) {
      await RNFS.mkdir(dirPath);
      console.log('Created directory:', dirPath);
    }
  } catch (error) {
    console.error('Error creating model directory:', error);
    throw error;
  }
};

/**
 * Delete a model file from storage
 */
export const deleteModelFile = async (model: Model): Promise<void> => {
  try {
    const filePath = await getModelFullPath(model);
    const exists = await RNFS.exists(filePath);
    
    if (exists) {
      await RNFS.unlink(filePath);
      console.log('Deleted model file:', filePath);
    } else {
      console.log('Model file does not exist:', filePath);
    }
  } catch (error) {
    console.error('Error deleting model file:', error);
    throw error;
  }
};

/**
 * Get available storage space
 */
export const getAvailableSpace = async (): Promise<number> => {
  try {
    const fsInfo = await RNFS.getFSInfo();
    return fsInfo.freeSpace;
  } catch (error) {
    console.error('Error getting available space:', error);
    return 0;
  }
};

/**
 * Check if there's enough space to download a model
 */
export const hasEnoughSpace = async (model: Model): Promise<boolean> => {
  try {
    const availableSpace = await getAvailableSpace();
    const requiredSpace = model.size;
    
    // Add 10% buffer for safety
    const spaceWithBuffer = requiredSpace * 1.1;
    
    return availableSpace >= spaceWithBuffer;
  } catch (error) {
    console.error('Error checking available space:', error);
    return false;
  }
};

/**
 * Format bytes to human readable format
 */
export const formatBytes = (bytes: number, decimals: number = 2): string => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
};

/**
 * Generate a random ID
 */
export const generateId = (): string => {
  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
};
