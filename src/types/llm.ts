import { LlamaContext } from '@pocketpalai/llama.rn';

export enum ModelOrigin {
  PRESET = 'preset',
  LOCAL = 'local',
  HF = 'hf',
}

export enum CacheType {
  F16 = 'f16',
  F32 = 'f32',
  Q8_0 = 'q8_0',
  Q4_0 = 'q4_0',
  Q4_1 = 'q4_1',
  IQ4_NL = 'iq4_nl',
  Q5_0 = 'q5_0',
  Q5_1 = 'q5_1',
}

export interface ChatTemplateConfig {
  name: string;
  addGenerationPrompt: boolean;
  bosToken?: string;
  eosToken?: string;
  chatTemplate?: string;
  systemPrompt?: string;
  addBosToken?: boolean;
  addEosToken?: boolean;
}

export interface CompletionParams {
  prompt?: string;
  n_predict?: number;
  temperature?: number;
  top_k?: number;
  top_p?: number;
  min_p?: number;
  penalty_repeat?: number;
  penalty_freq?: number;
  penalty_present?: number;
  mirostat?: number;
  mirostat_tau?: number;
  mirostat_eta?: number;
  seed?: number;
  stop?: string[];
  // App-specific parameters
  version?: number;
  include_thinking_in_context?: boolean;
}

export interface ModelFile {
  rfilename: string;
  size?: number;
  url?: string;
  oid?: string;
  lfs?: {
    oid: string;
    size: number;
    pointerSize: number;
  };
  canFitInStorage?: boolean;
}

export interface HuggingFaceModel {
  _id: string;
  id: string;
  author: string;
  gated: boolean | string;
  inference: string;
  lastModified: string;
  likes: number;
  trendingScore: number;
  private: boolean;
  sha: string;
  downloads: number;
  tags: string[];
  library_name: string;
  createdAt: string;
  model_id: string;
  siblings: ModelFile[];
  url?: string;
}

export interface Model {
  id: string;
  author: string;
  name: string;
  type?: string;
  capabilities?: string[];
  size: number; // Size in bytes
  params: number;
  isDownloaded: boolean;
  downloadUrl: string;
  hfUrl: string;
  progress: number; // Progress as a percentage
  downloadSpeed?: string;
  filename: string;
  fullPath?: string; // Full path for local models
  isLocal: boolean;
  origin: ModelOrigin;
  defaultChatTemplate: ChatTemplateConfig;
  chatTemplate: ChatTemplateConfig;
  defaultStopWords: string[];
  stopWords: string[];
  defaultCompletionSettings: CompletionParams;
  completionSettings: CompletionParams;
  hfModelFile?: ModelFile;
  hfModel?: HuggingFaceModel;
  hash?: string;
}

export interface ContextSettings {
  n_context: number;
  n_batch: number;
  n_ubatch: number;
  n_threads: number;
  flash_attn: boolean;
  cache_type_k: CacheType;
  cache_type_v: CacheType;
  n_gpu_layers: number;
}

export interface ChatMessage {
  role: 'system' | 'assistant' | 'user';
  content: string;
}

export interface MessageType {
  id: string;
  author: { id: string };
  createdAt: number;
  text: string;
  type: 'text';
  metadata?: {
    contextId?: string;
    conversationId?: string;
    copyable?: boolean;
    system?: boolean;
    timings?: any;
  };
}

export interface User {
  id: string;
  firstName?: string;
  lastName?: string;
}

export interface DownloadProgress {
  bytesDownloaded: number;
  bytesTotal: number;
  progress: number;
  speed: string;
  eta: string;
  rawSpeed: number;
  rawEta: number;
}

export interface DownloadJob {
  model: Model;
  state: {
    isDownloading: boolean;
    progress: DownloadProgress | null;
    error: Error | null;
  };
  destination: string;
  lastBytesWritten: number;
  lastUpdateTime: number;
  jobId?: number; // iOS
  downloadId?: string; // Android
}

export interface DownloadEventCallbacks {
  onStart?: (modelId: string) => void;
  onProgress?: (modelId: string, progress: DownloadProgress) => void;
  onComplete?: (modelId: string) => void;
  onError?: (modelId: string, error: Error) => void;
}

// TODO: Add more types as needed for chat sessions, UI state, etc.
