import {Model} from '../../utils/types';

export interface DownloadProgress {
  bytesDownloaded: number;
  bytesTotal: number;
  progress: number;
  speed: string;
  eta: string;
  rawSpeed: number;
  rawEta: number;
}

export interface DownloadState {
  isDownloading: boolean;
  progress: DownloadProgress | null;
  error: Error | null;
}

export interface DownloadJob {
  model: Model;
  state: DownloadState;
  destination: string;
  lastBytesWritten: number;
  lastUpdateTime: number;
  jobId?: number; // iOS download job ID
  downloadId?: string; // Android download ID
}

export type DownloadMap = Map<string, DownloadJob>;

export interface DownloadEventCallbacks {
  onStart?: (modelId: string) => void;
  onProgress?: (modelId: string, progress: DownloadProgress) => void;
  onComplete?: (modelId: string) => void;
  onError?: (modelId: string, error: Error) => void;
}
