import { makeAutoObservable, runInAction } from 'mobx';
import { makePersistable } from 'mobx-persist-store';
import AsyncStorage from '@react-native-async-storage/async-storage';

import { MessageType, CompletionParams, User } from '../types/llm';
import { generateId } from '../utils/fileSystem';
import { defaultCompletionParams } from '../utils/chatTemplates';

export interface ChatSession {
  id: string;
  title: string;
  createdAt: number;
  updatedAt: number;
  messages: MessageType[];
  completionSettings: CompletionParams;
}

export class ChatSessionStore {
  // Session management
  sessions: ChatSession[] = [];
  activeSessionId: string | null = null;
  
  // Message state
  isGenerating: boolean = false;
  
  // Global settings for new chats
  globalCompletionSettings: CompletionParams = { ...defaultCompletionParams };
  
  // Edit mode
  isEditMode: boolean = false;
  editingMessageId: string | null = null;

  constructor() {
    makeAutoObservable(this);
    
    // Make store persistent
    makePersistable(this, {
      name: 'ChatSessionStore',
      properties: [
        'sessions',
        'activeSessionId',
        'globalCompletionSettings',
      ],
      storage: AsyncStorage,
    });
  }

  // Getters
  get activeSession(): ChatSession | undefined {
    if (!this.activeSessionId) return undefined;
    return this.sessions.find(s => s.id === this.activeSessionId);
  }

  get currentMessages(): MessageType[] {
    const session = this.activeSession;
    if (!session) return [];
    
    if (this.isEditMode && this.editingMessageId) {
      const messageIndex = session.messages.findIndex(
        msg => msg.id === this.editingMessageId
      );
      if (messageIndex >= 0) {
        // Return messages after the editing message
        return session.messages.slice(messageIndex + 1);
      }
    }
    
    return session.messages;
  }

  get hasActiveSession(): boolean {
    return this.activeSessionId !== null && this.activeSession !== undefined;
  }

  // Session management methods
  createNewSession(title: string = 'New Chat'): ChatSession {
    const session: ChatSession = {
      id: generateId(),
      title,
      createdAt: Date.now(),
      updatedAt: Date.now(),
      messages: [],
      completionSettings: { ...this.globalCompletionSettings },
    };

    runInAction(() => {
      this.sessions.unshift(session); // Add to beginning
      this.activeSessionId = session.id;
    });

    return session;
  }

  setActiveSession(sessionId: string | null) {
    runInAction(() => {
      this.activeSessionId = sessionId;
      this.exitEditMode();
    });
  }

  deleteSession(sessionId: string) {
    const sessionIndex = this.sessions.findIndex(s => s.id === sessionId);
    if (sessionIndex === -1) return;

    runInAction(() => {
      this.sessions.splice(sessionIndex, 1);
      
      // If we deleted the active session, clear it
      if (this.activeSessionId === sessionId) {
        this.activeSessionId = null;
      }
    });
  }

  updateSessionTitle(sessionId: string, title: string) {
    const session = this.sessions.find(s => s.id === sessionId);
    if (!session) return;

    runInAction(() => {
      session.title = title;
      session.updatedAt = Date.now();
    });
  }

  // Message management methods
  addMessage(message: Omit<MessageType, 'id'>): MessageType {
    if (!this.activeSession) {
      // Create a new session if none exists
      this.createNewSession();
    }

    const fullMessage: MessageType = {
      ...message,
      id: generateId(),
    };

    const session = this.activeSession!;
    
    runInAction(() => {
      session.messages.unshift(fullMessage); // Add to beginning (newest first)
      session.updatedAt = Date.now();
      
      // Auto-update title based on first user message
      if (session.title === 'New Chat' && message.author.id === 'user' && message.text) {
        const titleText = message.text.length > 50 
          ? message.text.substring(0, 50) + '...'
          : message.text;
        session.title = titleText;
      }
    });

    return fullMessage;
  }

  updateMessage(messageId: string, updates: Partial<MessageType>) {
    const session = this.activeSession;
    if (!session) return;

    const messageIndex = session.messages.findIndex(m => m.id === messageId);
    if (messageIndex === -1) return;

    runInAction(() => {
      session.messages[messageIndex] = {
        ...session.messages[messageIndex],
        ...updates,
      };
      session.updatedAt = Date.now();
    });
  }

  deleteMessage(messageId: string) {
    const session = this.activeSession;
    if (!session) return;

    const messageIndex = session.messages.findIndex(m => m.id === messageId);
    if (messageIndex === -1) return;

    runInAction(() => {
      session.messages.splice(messageIndex, 1);
      session.updatedAt = Date.now();
    });
  }

  // Generation state methods
  setIsGenerating(isGenerating: boolean) {
    runInAction(() => {
      this.isGenerating = isGenerating;
    });
  }

  // Edit mode methods
  enterEditMode(messageId: string) {
    runInAction(() => {
      this.isEditMode = true;
      this.editingMessageId = messageId;
    });
  }

  exitEditMode() {
    runInAction(() => {
      this.isEditMode = false;
      this.editingMessageId = null;
    });
  }

  commitEdit() {
    if (!this.editingMessageId) return;

    // Remove messages after the edited message (including the edited message)
    this.removeMessagesFromId(this.editingMessageId, true);
    this.exitEditMode();
  }

  removeMessagesFromId(messageId: string, includeMessage: boolean = true) {
    const session = this.activeSession;
    if (!session) return;

    const messageIndex = session.messages.findIndex(m => m.id === messageId);
    if (messageIndex === -1) return;

    const endIndex = includeMessage ? messageIndex + 1 : messageIndex;
    
    runInAction(() => {
      session.messages.splice(0, endIndex); // Remove from beginning
      session.updatedAt = Date.now();
    });
  }

  // Settings methods
  updateGlobalCompletionSettings(settings: Partial<CompletionParams>) {
    runInAction(() => {
      this.globalCompletionSettings = {
        ...this.globalCompletionSettings,
        ...settings,
      };
    });
  }

  updateSessionCompletionSettings(settings: Partial<CompletionParams>) {
    const session = this.activeSession;
    if (!session) return;

    runInAction(() => {
      session.completionSettings = {
        ...session.completionSettings,
        ...settings,
      };
      session.updatedAt = Date.now();
    });
  }

  // Utility methods
  clearAllSessions() {
    runInAction(() => {
      this.sessions = [];
      this.activeSessionId = null;
      this.exitEditMode();
    });
  }

  getSessionById(sessionId: string): ChatSession | undefined {
    return this.sessions.find(s => s.id === sessionId);
  }
}
