import { makeAutoObservable, runInAction } from 'mobx';
import { makePersistable } from 'mobx-persist-store';
import AsyncStorage from '@react-native-async-storage/async-storage';

export class UIStore {
  // Theme and appearance
  isDarkMode: boolean = false;
  
  // Language and localization
  language: string = 'en';
  
  // App settings
  keepScreenAwake: boolean = true;
  showPerformanceMetrics: boolean = false;
  
  // Error handling
  lastError: string | null = null;
  
  // Loading states
  isLoading: boolean = false;
  loadingMessage: string = '';
  
  // Notifications and messages
  snackbarMessage: string = '';
  snackbarVisible: boolean = false;
  
  // Model management UI
  showModelSettings: boolean = false;
  selectedModelId: string | undefined = undefined;
  
  // Chat UI
  chatInputText: string = '';
  showChatSettings: boolean = false;

  constructor() {
    makeAutoObservable(this);
    
    // Make store persistent
    makePersistable(this, {
      name: 'UIStore',
      properties: [
        'isDarkMode',
        'language',
        'keepScreenAwake',
        'showPerformanceMetrics',
      ],
      storage: AsyncStorage,
    });
  }

  // Theme methods
  setDarkMode(isDark: boolean) {
    runInAction(() => {
      this.isDarkMode = isDark;
    });
  }

  toggleDarkMode() {
    this.setDarkMode(!this.isDarkMode);
  }

  // Language methods
  setLanguage(language: string) {
    runInAction(() => {
      this.language = language;
    });
  }

  // Settings methods
  setKeepScreenAwake(keep: boolean) {
    runInAction(() => {
      this.keepScreenAwake = keep;
    });
  }

  setShowPerformanceMetrics(show: boolean) {
    runInAction(() => {
      this.showPerformanceMetrics = show;
    });
  }

  // Error handling methods
  setError(error: string | null) {
    runInAction(() => {
      this.lastError = error;
    });
  }

  clearError() {
    this.setError(null);
  }

  showError(message: string) {
    this.setError(message);
    this.showSnackbar(message);
  }

  // Loading state methods
  setLoading(isLoading: boolean, message: string = '') {
    runInAction(() => {
      this.isLoading = isLoading;
      this.loadingMessage = message;
    });
  }

  startLoading(message: string = 'Loading...') {
    this.setLoading(true, message);
  }

  stopLoading() {
    this.setLoading(false, '');
  }

  // Snackbar methods
  showSnackbar(message: string) {
    runInAction(() => {
      this.snackbarMessage = message;
      this.snackbarVisible = true;
    });
  }

  hideSnackbar() {
    runInAction(() => {
      this.snackbarVisible = false;
      this.snackbarMessage = '';
    });
  }

  // Model UI methods
  setShowModelSettings(show: boolean) {
    runInAction(() => {
      this.showModelSettings = show;
    });
  }

  setSelectedModelId(modelId: string | undefined) {
    runInAction(() => {
      this.selectedModelId = modelId;
    });
  }

  // Chat UI methods
  setChatInputText(text: string) {
    runInAction(() => {
      this.chatInputText = text;
    });
  }

  clearChatInput() {
    this.setChatInputText('');
  }

  setShowChatSettings(show: boolean) {
    runInAction(() => {
      this.showChatSettings = show;
    });
  }

  // Utility methods
  reset() {
    runInAction(() => {
      this.lastError = null;
      this.isLoading = false;
      this.loadingMessage = '';
      this.snackbarMessage = '';
      this.snackbarVisible = false;
      this.showModelSettings = false;
      this.selectedModelId = undefined;
      this.chatInputText = '';
      this.showChatSettings = false;
    });
  }
}
