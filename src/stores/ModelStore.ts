import { makeAutoObservable, runInAction } from 'mobx';
import { makePersistable } from 'mobx-persist-store';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform, AppState, AppStateStatus } from 'react-native';
import { LlamaContext, initLlama } from '@pocketpalai/llama.rn';

import { 
  Model, 
  ModelOrigin, 
  CacheType, 
  ContextSettings,
  CompletionParams 
} from '../types/llm';
import { 
  getModelFullPath, 
  checkModelFileExists, 
  deleteModelFile,
  generateId 
} from '../utils/fileSystem';
import { 
  getLocalModelDefaultSettings, 
  getHFDefaultSettings,
  defaultCompletionParams 
} from '../utils/chatTemplates';

export class ModelStore {
  // Model management
  models: Model[] = [];
  activeModelId: string | undefined = undefined;
  lastUsedModelId: string | undefined = undefined;

  // Context management
  context: LlamaContext | undefined = undefined;
  isContextLoading: boolean = false;
  loadingModel: Model | undefined = undefined;

  // Context settings
  n_context: number = 2048;
  n_gpu_layers: number = 50;
  n_threads: number = 4;
  max_threads: number = 4;
  flash_attn: boolean = false;
  cache_type_k: CacheType = CacheType.F16;
  cache_type_v: CacheType = CacheType.F16;
  n_batch: number = 512;
  n_ubatch: number = 512;

  // App state management
  appState: AppStateStatus = AppState.currentState;
  useAutoRelease: boolean = true;
  useMetal: boolean = false;

  // Inference state
  inferencing: boolean = false;
  isStreaming: boolean = false;

  // Active context settings (for tracking what was used to initialize)
  activeContextSettings: ContextSettings | undefined = undefined;

  constructor() {
    makeAutoObservable(this);
    this.initializeThreadCount();
    this.initializeUseMetal();
    this.setupAppStateListener();

    // Make store persistent
    makePersistable(this, {
      name: 'ModelStore',
      properties: [
        'models',
        'useAutoRelease',
        'n_gpu_layers',
        'useMetal',
        'n_context',
        'n_threads',
        'flash_attn',
        'cache_type_k',
        'cache_type_v',
        'n_batch',
        'n_ubatch',
        'lastUsedModelId',
      ],
      storage: AsyncStorage,
    }).then(() => {
      this.initializeStore();
    });
  }

  private async initializeThreadCount() {
    try {
      // TODO: Implement device CPU detection
      // For now, use a reasonable default
      const cores = 4; // Default assumption
      this.max_threads = cores;

      runInAction(() => {
        if (cores <= 4) {
          this.n_threads = cores;
        } else {
          this.n_threads = Math.floor(cores * 0.8);
        }
      });
    } catch (error) {
      console.error('Failed to get CPU info:', error);
      runInAction(() => {
        this.max_threads = 4;
        this.n_threads = 4;
      });
    }
  }

  private initializeUseMetal() {
    // Enable Metal only on iOS 18+
    const isIOS18OrHigher =
      Platform.OS === 'ios' && parseInt(Platform.Version as string, 10) >= 18;
    
    if (!isIOS18OrHigher) {
      runInAction(() => {
        this.useMetal = false;
      });
    }
  }

  private setupAppStateListener() {
    AppState.addEventListener('change', this.handleAppStateChange);
  }

  private handleAppStateChange = async (nextAppState: AppStateStatus) => {
    if (
      this.appState.match(/inactive|background/) &&
      nextAppState === 'active'
    ) {
      if (this.useAutoRelease && this.lastUsedModelId) {
        await this.reinitializeContext();
      }
    } else if (
      this.appState === 'active' &&
      nextAppState.match(/inactive|background/)
    ) {
      if (this.useAutoRelease) {
        await this.releaseContext();
      }
    }

    runInAction(() => {
      this.appState = nextAppState;
    });
  };

  private async reinitializeContext() {
    if (this.lastUsedModelId) {
      const model = this.models.find(m => m.id === this.lastUsedModelId);
      if (model && model.isDownloaded) {
        await this.initContext(model);
      }
    }
  }

  private async initializeStore() {
    // Initialize download status for existing models
    await this.refreshDownloadStatuses();
    
    // TODO: Load default models if none exist
    if (this.models.length === 0) {
      console.log('No models found, will need to add default models');
    }
  }

  // Getters
  get activeModel(): Model | undefined {
    return this.models.find(model => model.id === this.activeModelId);
  }

  get availableModels(): Model[] {
    return this.models.filter(model => model.isDownloaded);
  }

  get effectiveValues() {
    const effectiveContext = this.n_context;
    const effectiveBatch = Math.min(this.n_batch, effectiveContext);
    const effectiveUBatch = Math.min(this.n_ubatch, effectiveBatch);

    return {
      n_context: effectiveContext,
      n_batch: effectiveBatch,
      n_ubatch: effectiveUBatch,
    };
  }

  // Context management methods
  async initContext(model: Model): Promise<LlamaContext> {
    await this.releaseContext();
    
    const filePath = await getModelFullPath(model);
    if (!filePath) {
      throw new Error('Model path is undefined');
    }

    const exists = await checkModelFileExists(model);
    if (!exists) {
      throw new Error('Model file does not exist');
    }

    runInAction(() => {
      this.isContextLoading = true;
      this.loadingModel = model;
    });

    try {
      const effectiveValues = this.effectiveValues;
      const initSettings: ContextSettings = {
        n_context: effectiveValues.n_context,
        n_batch: effectiveValues.n_batch,
        n_ubatch: effectiveValues.n_ubatch,
        n_threads: this.n_threads,
        flash_attn: this.flash_attn,
        cache_type_k: this.cache_type_k,
        cache_type_v: this.cache_type_v,
        n_gpu_layers: this.useMetal ? this.n_gpu_layers : 0,
      };

      const ctx = await initLlama({
        model: filePath,
        use_mlock: true,
        ...initSettings,
        use_progress_callback: true,
      });

      runInAction(() => {
        this.context = ctx;
        this.activeContextSettings = initSettings;
        this.activeModelId = model.id;
        this.lastUsedModelId = model.id;
      });

      console.log('Model context initialized successfully:', model.name);
      return ctx;
    } finally {
      runInAction(() => {
        this.isContextLoading = false;
        this.loadingModel = undefined;
      });
    }
  }

  async releaseContext(): Promise<void> {
    console.log('Attempting to release context');
    
    if (!this.context) {
      return;
    }

    try {
      await this.context.release();
      console.log('Context released successfully');
    } catch (error) {
      console.error('Error releasing context:', error);
    }

    runInAction(() => {
      this.context = undefined;
      this.activeContextSettings = undefined;
      this.inferencing = false;
      this.isStreaming = false;
    });
  }

  async manualReleaseContext(): Promise<void> {
    await this.releaseContext();
    runInAction(() => {
      this.activeModelId = undefined;
    });
  }

  // Model management methods
  async addLocalModel(localFilePath: string, name?: string): Promise<Model> {
    const filename = localFilePath.split('/').pop();
    if (!filename) {
      throw new Error('Invalid local file path');
    }

    const defaultSettings = getLocalModelDefaultSettings();

    const model: Model = {
      id: generateId(),
      author: '',
      name: name || filename,
      size: 0, // Will be determined later
      params: 0,
      isDownloaded: true,
      downloadUrl: '',
      hfUrl: '',
      progress: 100,
      filename,
      fullPath: localFilePath,
      isLocal: true,
      origin: ModelOrigin.LOCAL,
      defaultChatTemplate: { ...defaultSettings.chatTemplate },
      chatTemplate: { ...defaultSettings.chatTemplate },
      defaultStopWords: [],
      stopWords: [],
      defaultCompletionSettings: { ...defaultSettings.completionParams },
      completionSettings: { ...defaultSettings.completionParams },
    };

    runInAction(() => {
      this.models.push(model);
    });

    await this.refreshDownloadStatuses();
    return model;
  }

  async deleteModel(model: Model): Promise<void> {
    const modelIndex = this.models.findIndex(m => m.id === model.id);
    if (modelIndex === -1) {
      return;
    }

    // Release context if this model is active
    if (this.activeModelId === model.id) {
      await this.releaseContext();
      runInAction(() => {
        this.activeModelId = undefined;
      });
    }

    // Delete file for local models, or just mark as not downloaded for others
    if (model.isLocal || model.origin === ModelOrigin.LOCAL) {
      try {
        await deleteModelFile(model);
        runInAction(() => {
          this.models.splice(modelIndex, 1);
        });
      } catch (error) {
        console.error('Failed to delete local model file:', error);
      }
    } else {
      try {
        await deleteModelFile(model);
        runInAction(() => {
          model.isDownloaded = false;
          model.progress = 0;
        });
      } catch (error) {
        console.error('Failed to delete model file:', error);
      }
    }
  }

  async refreshDownloadStatuses(): Promise<void> {
    for (const model of this.models) {
      const exists = await checkModelFileExists(model);
      runInAction(() => {
        model.isDownloaded = exists;
        if (exists && model.progress < 100) {
          model.progress = 100;
        }
      });
    }
  }

  // Settings methods
  setNThreads(n_threads: number) {
    runInAction(() => {
      this.n_threads = Math.min(n_threads, this.max_threads);
    });
  }

  setNContext(n_context: number) {
    runInAction(() => {
      this.n_context = Math.max(n_context, 200); // Minimum context size
    });
  }

  setNGPULayers(n_gpu_layers: number) {
    runInAction(() => {
      this.n_gpu_layers = Math.max(0, n_gpu_layers);
    });
  }

  setUseMetal(useMetal: boolean) {
    runInAction(() => {
      this.useMetal = useMetal;
    });
  }

  setUseAutoRelease(useAutoRelease: boolean) {
    runInAction(() => {
      this.useAutoRelease = useAutoRelease;
    });
  }

  setInferencing(value: boolean) {
    runInAction(() => {
      this.inferencing = value;
    });
  }

  setIsStreaming(value: boolean) {
    runInAction(() => {
      this.isStreaming = value;
    });
  }

  // Utility methods
  isModelAvailable(modelId?: string): boolean {
    if (!modelId) return false;
    return this.availableModels.some(m => m.id === modelId);
  }
}
