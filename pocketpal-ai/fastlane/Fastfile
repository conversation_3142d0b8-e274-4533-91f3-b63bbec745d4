desc "Bump version for both Android and iOS"
lane :bump_version do |options|
  version_type = options[:version_type] || "patch"
  root_dir = File.expand_path('..', Dir.pwd)
  
  # Common paths
  package_json_path = File.join(root_dir, 'package.json')
  version_file_path = File.join(root_dir, '.version')

  # Bump version in package.json
  Dir.chdir(root_dir) do
    sh("yarn version --#{version_type} --no-git-tag-version")
  end
  
  new_version = JSON.parse(File.read(package_json_path))['version']
  sh("echo #{new_version} > #{version_file_path}")

  # For Android: Read current version code and increment it
  gradle_file = File.join(root_dir, "android/app/build.gradle")
  current_version_code = android_get_version_code(gradle_file: gradle_file).to_i
  new_version_code = current_version_code + 1

  # Update Android version
  android_set_version_code(
    gradle_file: gradle_file,
    version_code: new_version_code
  )
  
  android_set_version_name(
    gradle_file: gradle_file,
    version_name: new_version
  )

  # For iOS: Bump version and build number
  ios_dir = File.join(root_dir, 'ios')
  Dir.chdir(ios_dir) do
    increment_version_number_in_xcodeproj(
      version_number: new_version,
      xcodeproj: File.join(ios_dir, "PocketPal.xcodeproj")
    )
    increment_build_number_in_xcodeproj(
      xcodeproj: File.join(ios_dir, "PocketPal.xcodeproj")
    )
  end

  UI.message("Bumped versions:")
  UI.message("- Version name: #{new_version}")
  UI.message("- Android version code: #{new_version_code}")
  UI.message("- iOS build number: #{get_build_number_from_xcodeproj(xcodeproj: File.join(ios_dir, 'PocketPal.xcodeproj'))}")
end
