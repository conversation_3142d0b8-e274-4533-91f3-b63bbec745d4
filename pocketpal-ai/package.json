{"name": "PocketPal", "version": "1.9.4", "private": true, "scripts": {"prepare": "husky", "postinstall": "patch-package", "android": "react-native run-android", "build:android": "cd android && ./gradlew assembleDebug && cd ..", "build:android:release": "cd android && ./gradlew bundleRelease && cd ..", "ios": "react-native run-ios --simulator=\"iPhone 16 Pro\"", "ios:build": "cd ios && xcodebuild CC=clang CPLUSPLUS=clang++ LD=clang LDPLUSPLUS=clang++ -workspace PocketPal.xcworkspace -scheme PocketPal -configuration Debug -sdk iphonesimulator -arch $(uname -m) ONLY_ACTIVE_ARCH=YES GCC_OPTIMIZATION_LEVEL=0 GCC_PRECOMPILE_PREFIX_HEADER=YES ASSETCATALOG_COMPILER_OPTIMIZATION=time DEBUG_INFORMATION_FORMAT=dwarf COMPILER_INDEX_STORE_ENABLE=NO | xcpretty", "ios:build:release": "cd ios && xcodebuild CC=clang CPLUSPLUS=clang++ LD=clang LDPLUSPLUS=clang++ -workspace PocketPal.xcworkspace -scheme PocketPal -configuration Release -sdk iphoneos -arch arm64 -arch x86_64 GCC_OPTIMIZATION_LEVEL=s GCC_PRECOMPILE_PREFIX_HEADER=YES ASSETCATALOG_COMPILER_OPTIMIZATION=space DEBUG_INFORMATION_FORMAT=dwarf-with-dsym COMPILER_INDEX_STORE_ENABLE=YES | xcpretty", "clean:ios": "cd ios && xcodebuild clean -workspace PocketPal.xcworkspace -scheme PocketPal -configuration Debug && cd ..", "clean:android": "cd android && ./gradlew clean && cd ..", "clean": "yarn clean:ios && yarn clean:android", "lint": "eslint .", "typecheck": "tsc --noEmit", "lint:fix": "eslint \"**/*.{js,ts,tsx}\" --fix", "format": "prettier --write \"**/*.{js,ts,tsx,json,md}\"", "start": "react-native start", "start:reset": "react-native start --reset-cache", "test": "jest"}, "dependencies": {"@dr.pogodin/react-native-fs": "^2.30.3", "@flyerhq/react-native-link-preview": "^1.6.0", "@gorhom/bottom-sheet": "^5.0.6", "@hookform/resolvers": "^3.10.0", "@nozbe/watermelondb": "^0.28.0", "@pocketpalai/llama.rn": "0.5.8-1", "@react-native-async-storage/async-storage": "^2.1.0", "@react-native-clipboard/clipboard": "^1.15.0", "@react-native-community/blur": "^4.4.1", "@react-native-community/slider": "^4.5.5", "@react-native-firebase/app": "21.6.1", "@react-native-firebase/app-check": "21.6.1", "@react-native-masked-view/masked-view": "^0.3.1", "@react-native-picker/picker": "^2.10.2", "@react-navigation/bottom-tabs": "^6.6.1", "@react-navigation/drawer": "^6.6.15", "@react-navigation/native": "^6.1.17", "@react-navigation/stack": "^6.3.29", "axios": "^1.8.2", "chat-formatter": "^0.3.4", "date-fns": "^3.6.0", "dayjs": "^1.11.11", "mobx": "^6.12.3", "mobx-persist-store": "^1.1.5", "mobx-react": "^9.1.1", "react": "18.3.1", "react-hook-form": "^7.54.2", "react-native": "0.76.3", "react-native-config": "^1.5.1", "react-native-device-info": "^14.0.4", "react-native-document-picker": "^9.1.2", "react-native-gesture-handler": "^2.20.2", "react-native-get-random-values": "^1.11.0", "react-native-haptic-feedback": "^2.3.3", "react-native-image-viewing": "^0.2.2", "react-native-keyboard-controller": "^1.16.4", "react-native-keychain": "^10.0.0", "react-native-linear-gradient": "^2.8.3", "react-native-marked": "^6.0.4", "react-native-modal": "^13.0.1", "react-native-paper": "^5.12.5", "react-native-parsed-text": "^0.0.22", "react-native-picker-select": "^9.1.3", "react-native-reanimated": "^3.16.3", "react-native-render-html": "^6.3.4", "react-native-safe-area-context": "^4.14.0", "react-native-screens": "^4.8.0", "react-native-share": "^12.0.9", "react-native-svg": "^15.11.1", "react-native-vector-icons": "^10.1.0", "tinycolor2": "^1.6.0", "uuid": "^10.0.0", "zod": "^3.24.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/plugin-proposal-decorators": "^7.27.1", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.27.1", "@commitlint/cli": "^19.6.0", "@commitlint/config-conventional": "^19.6.0", "@react-native-community/cli": "15.0.1", "@react-native-community/cli-platform-android": "15.0.1", "@react-native-community/cli-platform-ios": "15.0.1", "@react-native/babel-preset": "0.76.3", "@react-native/eslint-config": "0.73.2", "@react-native/metro-config": "0.76.3", "@react-native/typescript-config": "0.76.3", "@testing-library/react-hooks": "^8.0.1", "@testing-library/react-native": "^12.9.0", "@types/jest": "^29.5.14", "@types/lodash": "^4.17.13", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.0.0", "@types/uuid": "^10.0.0", "babel-jest": "^29.6.3", "eslint": "^8.19.0", "husky": "^9.1.7", "jest": "^29.6.3", "patch-package": "^8.0.0", "postinstall-postinstall": "^2.1.0", "prettier": "2.8.8", "react-native-dotenv": "^3.4.11", "react-native-svg-transformer": "^1.5.0", "react-test-renderer": "18.3.1", "typescript": "5.0.4"}, "engines": {"node": ">=18"}, "packageManager": "yarn@1.22.22"}