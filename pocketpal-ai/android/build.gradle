buildscript {
    ext {
        buildToolsVersion = "35.0.0"
        minSdkVersion = 24
        compileSdkVersion = 35
        targetSdkVersion = 35
        ndkVersion = "26.1.10909125"
        kotlinVersion = "1.9.24"
    }
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath("com.android.tools.build:gradle")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin")

        // Add the Google Services plugin dependency
        // This is used exclusively for sending benchmarks (with user consent) to Hugging Face Spaces via Firebase.
        // Firebase is used for App Check functionality, allowing unauthenticated users to submit their benchmark data securely.
        classpath 'com.google.gms:google-services:4.4.2'
    }
}

apply plugin: "com.facebook.react.rootproject"
