<?xml version="1.0" encoding="utf-8"?>
<data-extraction-rules>
    <cloud-backup disableIfNoEncryptionCapabilities="true">
        <exclude domain="file" path="models/"/>
        <!-- only created in debuggable builds, but can be large and exceed
         25mb cloud backup limit which makes dev testing difficult -->
        <exclude domain="file" path="BridgeReactNativeDevBundle.js"/>
        <exclude domain="root" path="code_cache/"/>
    </cloud-backup>

    <device-transfer>
        <exclude domain="file" path="models/"/>
        <exclude domain="file" path="BridgeReactNativeDevBundle.js"/>
        <exclude domain="root" path="code_cache/"/>
    </device-transfer>
</data-extraction-rules>