<manifest xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- Allow app to check if any other app can handle URLs -->
    <queries>
        <intent>
            <action android:name="android.intent.action.VIEW" />
            <data android:scheme="https"/>
        </intent>
    </queries>

    <uses-permission android:name="android.permission.INTERNET" />

    <!-- Needed only for devices running Android 9 (API 28) or lower -->
    <!-- Currently it is used only for storing chat sessions (/Download)-->
    <uses-permission
        android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="28"/>
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="28"/>

    <uses-sdk android:minSdkVersion="23" android:targetSdkVersion="35" />

    <application
      android:name=".MainApplication"
      android:label="@string/app_name"
      android:icon="@mipmap/ic_launcher"
      android:roundIcon="@mipmap/ic_launcher_round"
      android:dataExtractionRules="@xml/backup_rules_12_plus"
      android:fullBackupContent="@xml/backup_rules_legacy"
      android:theme="@style/AppTheme"
      android:supportsRtl="true">
      <activity
        android:name=".MainActivity"
        android:label="@string/app_name"
        android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
        android:launchMode="singleTask"
        android:windowSoftInputMode="adjustResize"
        android:exported="true">
        <intent-filter>
            <action android:name="android.intent.action.MAIN" />
            <category android:name="android.intent.category.LAUNCHER" />
        </intent-filter>
      </activity>
    </application>
</manifest>
