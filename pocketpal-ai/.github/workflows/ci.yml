name: CI Pipeline

on:
  pull_request:
    branches:
      - main
  push:
    branches:
      - main

permissions:
  contents: read

jobs:
  # Job for linting, type checking, unit testing
  build-and-test:
    runs-on: ubuntu-latest
    steps:
      # Step 1: Check out the repository
      - name: Check out code
        uses: actions/checkout@v3

      # Cache node_modules
      - name: Cache node_modules
        uses: actions/cache@v3
        with:
          path: node_modules
          key: ${{ runner.os }}-node-${{ hashFiles('yarn.lock') }}

      # Step 2: Set up Node.js environment
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'

      - name: Install Yarn
        run: npm install -g yarn

      # Step 3: Install dependencies and iOS Pods
      - name: Install dependencies
        run: yarn install

      # Step 4: Run linters (ESLint)
      - name: Run ESLint
        run: yarn lint

      # Step 5: Run TypeScript type checks
      - name: Run TypeScript type check
        run: yarn typecheck

      # Step 6: Run unit tests (Jest)
      - name: Run unit tests
        run: yarn test --coverage

  # Job for Android build
  build-android:
    runs-on: ubuntu-latest
    needs: build-and-test
    steps:
      - name: Check out code
        uses: actions/checkout@v3

      # Cache node_modules
      - name: Cache node_modules
        uses: actions/cache@v3
        with:
          path: node_modules
          key: ${{ runner.os }}-node-${{ hashFiles('yarn.lock') }}

      - name: Install Yarn
        run: npm install -g yarn

      - name: Install dependencies
        run: yarn install

      - name: Set up JDK 17
        uses: actions/setup-java@v3
        with:
          java-version: '17'
          distribution: 'temurin'
          cache: 'gradle'

      - name: Create dummy google-services.json for CI
        run: |
          cat > android/app/google-services.json << 'EOL'
          {
            "project_info": {
              "project_number": "000000000000",
              "project_id": "dummy-project-for-ci",
              "storage_bucket": "dummy-project-for-ci.appspot.com"
            },
            "client": [{
              "client_info": {
                "mobilesdk_app_id": "1:000000000000:android:0000000000000000",
                "android_client_info": {
                  "package_name": "com.pocketpalai"
                }
              },
              "api_key": [{
                "current_key": "dummy-api-key-for-ci-builds"
              }]
            }]
          }
          EOL

      - name: Build Android
        run: yarn build:android # TODO: change to build:android:release

      - name: Upload Android APK
        uses: actions/upload-artifact@v4
        with:
          name: android-debug-apk # TODO: change to release-apk
          path: android/app/build/outputs/apk/debug/app-debug.apk

  # Job for iOS build
  build-ios:
    runs-on: macos-15  # macOS 15 with Xcode 16
    needs: build-and-test
    steps:
      - name: Check out code
        uses: actions/checkout@v3

      # Cache node_modules
      - name: Cache node_modules
        uses: actions/cache@v3
        with:
          path: node_modules
          key: ${{ runner.os }}-node-${{ hashFiles('yarn.lock') }}

      - name: Install Yarn
        run: npm install -g yarn

      - name: Install dependencies
        run: yarn install
      
      - name: Install CocoaPods dependencies
        run: |
          cd ios
          pod install
          cd ..

      - name: Build iOS
        run: yarn ios:build
