// Mock Q for queries
export const Q = {
  where: jest.fn(() => ({})),
  eq: jest.fn(() => ({})),
  gt: jest.fn(() => ({})),
  gte: jest.fn(() => ({})),
  lt: jest.fn(() => ({})),
  lte: jest.fn(() => ({})),
  between: jest.fn(() => ({})),
  oneOf: jest.fn(() => ({})),
  notIn: jest.fn(() => ({})),
  like: jest.fn(() => ({})),
  notLike: jest.fn(() => ({})),
  sanitizeLikeString: jest.fn(string => string),
  on: jest.fn(() => ({})),
  and: jest.fn(() => ({})),
  or: jest.fn(() => ({})),
  sortBy: jest.fn(() => ({})),
  desc: jest.fn(() => ({})),
  asc: jest.fn(() => ({})),
  take: jest.fn(() => ({})),
  skip: jest.fn(() => ({})),
  unsafe: jest.fn(() => ({})),
};
