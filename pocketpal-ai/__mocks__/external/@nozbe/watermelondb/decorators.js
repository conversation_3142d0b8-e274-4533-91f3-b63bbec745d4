// Mock decorators
export const text = () => (target, key) => {};
export const field = () => (target, key) => {};
export const date = () => (target, key) => {};
export const children = () => (target, key) => {};
export const relation = () => (target, key) => {};
export const readonly = () => (target, key) => {};
export const immutableRelation = () => (target, key) => {};
export const json = () => (target, key) => {};
export const nochange = () => (target, key) => {};
