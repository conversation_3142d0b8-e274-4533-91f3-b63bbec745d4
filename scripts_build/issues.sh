
https://github.com/mybigday/llama.rn/issues/97

 (ios/build/generated/ios/RNLlamaSpec/RNLlamaSpec.h:29:9)

  27 | #import <React/RCTCxxConvert.h>
  28 | #import <React/RCTManagedPointer.h>
> 29 | #import <"RCTTurboModule.h">
     |         ^ '"RCTTurboModule.h"' file not found
  30 | #import <optional>
  31 | #import <vector>
  32 | 

Can't compile: '"RCTTurboModule.h"' file not found #97

#import <"RCTTurboModule.h">
to

#import "RCTTurboModule.h"


RCT_NEW_ARCH_ENABLED=0