#* npm

npm install     @langchain/langgraph
npm install         @langchain/langgraph
npm install  crewai
npm install    dotenv
npm install    langchain
npm install    langserve
npm install    langsmith
npm install  stripe
npm install  sentry
npm install    firebase@^10.0.0
npm install firebaseui@^6.1.0
npm install @auth0/auth0-react

npm install @assistant-ui/react
npm install @ai-sdk/openai
npm install @assistant-ui/react-markdown
npm install @assistant-ui/react-langgraph
npm install @assistant-ui/react-ai-sdk
npm install @assistant-ui/react-syntax-highlighter
npm install @assistant-ui/react-playground
npm install @ai-sdk/react
npm install @eko-ai/eko

#* rust
cargo add genai
cargo add serde_json
cargo add langchain-rust
cargo add sentry

#* go
go get github.com/getsentry/sentry-go@latest
go get github.com/getsentry/sentry-go/http
go get firebase.google.com/go/v4@latest
go get github.com/seasonjs/stable-diffusion