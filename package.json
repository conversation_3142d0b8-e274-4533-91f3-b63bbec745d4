{"name": "diogenesaicompanion", "main": "expo-router/entry", "version": "1.0.4", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@dr.pogodin/react-native-fs": "^2.33.1", "@expo/vector-icons": "^14.0.4", "@flyerhq/react-native-chat-ui": "^1.4.3", "@flyerhq/react-native-link-preview": "^1.6.0", "@gorhom/bottom-sheet": "^5.0.6", "@huggingface/transformers": "^3.2.3", "@langchain/langgraph": "^0.2.27", "@mlc-ai/web-llm": "^0.2.77", "@nativescript/firebase-ui": "^3.3.2", "@pocketpalai/llama.rn": "^0.5.8-1", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-clipboard/clipboard": "^1.15.0", "@react-native-community/blur": "^4.4.1", "@react-native-community/cli-platform-ios": "^16.0.0", "@react-native-community/slider": "^4.5.5", "@react-native-firebase/analytics": "^21.7.1", "@react-native-firebase/app": "^21.7.1", "@react-native-firebase/app-check": "^21.7.1", "@react-native-firebase/auth": "^21.7.1", "@react-native-firebase/crashlytics": "^21.7.1", "@react-native-firebase/firestore": "^21.7.1", "@react-native-firebase/in-app-messaging": "^21.7.1", "@react-native-firebase/messaging": "^21.7.1", "@react-native-firebase/perf": "^21.7.1", "@react-native-firebase/remote-config": "^21.7.1", "@react-native-firebase/storage": "^21.7.1", "@react-native-masked-view/masked-view": "^0.3.1", "@react-native-picker/picker": "2.9.0", "@react-navigation/bottom-tabs": "^7.0.0", "@react-navigation/drawer": "^7.0.0", "@react-navigation/native": "^7.0.0", "@react-navigation/stack": "^7.1.1", "@sentry/react-native": "^6.5.0", "axios": "^1.7.7", "chat-formatter": "^0.3.4", "chromadb": "^1.9.4", "chromadb-default-embed": "^2.13.2", "crewai": "^1.0.1", "date-fns": "^3.6.0", "dayjs": "^1.11.11", "dotenv": "^16.4.7", "expo": "~52.0.28", "expo-blur": "~14.0.3", "expo-build-properties": "~0.13.2", "expo-camera": "~16.0.14", "expo-constants": "~17.0.5", "expo-dev-client": "~5.0.11", "expo-device": "~7.0.2", "expo-document-picker": "~13.0.2", "expo-file-system": "~18.0.8", "expo-font": "~13.0.3", "expo-haptics": "~14.0.1", "expo-linking": "~7.0.5", "expo-localization": "~16.0.1", "expo-notifications": "~0.29.13", "expo-router": "~4.0.17", "expo-secure-store": "~14.0.1", "expo-speech": "~13.0.1", "expo-speech-recognition": "^1.1.1", "expo-splash-screen": "~0.29.21", "expo-stable-diffusion": "^0.2.0", "expo-status-bar": "~2.0.1", "expo-symbols": "~0.2.2", "expo-system-ui": "~4.0.7", "expo-task-manager": "~12.0.5", "expo-web-browser": "~14.0.2", "firebase": "^11.2.0", "hermes-engine": "^0.10.0", "i18n-js": "^4.5.0", "langchain": "^0.3.6", "langserve": "^0.0.2", "langsmith": "^0.2.14", "mobx": "^6.13.7", "mobx-persist-store": "^1.1.8", "mobx-react": "^9.2.0", "openai": "^4.76.0", "react": "18.3.1", "react-dom": "18.3.1", "react-native": "^0.77.0", "react-native-blob-util": "^0.21.2", "react-native-config": "^1.5.3", "react-native-device-info": "^14.0.4", "react-native-document-picker": "^9.3.1", "react-native-gesture-handler": "~2.22.1", "react-native-get-random-values": "^1.11.0", "react-native-gifted-chat": "^2.6.5", "react-native-haptic-feedback": "^2.3.3", "react-native-image-viewing": "^0.2.2", "react-native-keyboard-controller": "^1.15.2", "react-native-linear-gradient": "^2.8.3", "react-native-markdown-display": "^7.0.2", "react-native-marked": "^6.0.4", "react-native-modal": "^13.0.1", "react-native-paper": "^5.12.5", "react-native-parsed-text": "^0.0.22", "react-native-picker-select": "^9.1.3", "react-native-purchases": "^8.4.0", "react-native-reanimated": "~3.16.1", "react-native-render-html": "^6.3.4", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.1.0", "react-native-svg": "^15.11.1", "react-native-toast-message": "^2.2.1", "react-native-vector-icons": "^10.2.0", "react-native-web": "~0.19.13", "react-native-webview": "13.12.5", "stripe": "^17.6.0", "uuid": "^11.0.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/plugin-proposal-decorators": "^7.25.9", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@commitlint/cli": "^19.6.0", "@commitlint/config-conventional": "^19.7.1", "@react-native-community/cli": "^16.0.2", "@react-native-community/cli-platform-android": "15.0.1", "@react-native-community/cli-platform-ios": "15.0.1", "@react-native/babel-preset": "0.76.3", "@react-native/eslint-config": "0.73.2", "@react-native/metro-config": "0.76.3", "@react-native/typescript-config": "0.76.3", "@testing-library/react": "^16.1.0", "@tsconfig/react-native": "^3.0.5", "@types/jest": "^29.5.14", "@types/lodash": "^4.17.13", "@types/react": "~18.3.12", "@types/react-native-vector-icons": "^6.4.18", "@types/react-test-renderer": "^18.3.1", "@types/uuid": "^10.0.0", "babel-jest": "^29.6.3", "eslint": "^8.19.0", "husky": "^9.1.7", "jest": "^29.2.1", "jest-expo": "~52.0.2", "patch-package": "^8.0.0", "postinstall-postinstall": "^2.1.0", "prettier": "2.8.8", "react-native-dotenv": "^3.4.11", "react-test-renderer": "18.3.1", "typescript": "^5.7.3"}, "private": true, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}