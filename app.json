{"expo": {"name": "DiogenesAICompanion", "slug": "diogenes-ai-companion", "version": "1.1.2", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "di<PERSON><PERSON><PERSON><PERSON>", "userInterfaceStyle": "automatic", "newArchEnabled": true, "jsEngine": "hermes", "ios": {"buildNumber": "1", "supportsTablet": true, "entitlements": {"aps-environment": "production", "com.apple.developer.kernel.increased-memory-limit": true}, "infoPlist": {"NSPhotoLibraryUsageDescription": "This app requires access to your photo library to allow you to upload and share photos."}, "bundleIdentifier": "com.diogenes.DiogenesAICompanion", "googleServicesFile": "./GoogleService-Info.plist"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "com.diogenes.DiogenesAICompanion", "googleServicesFile": "./google-services.json", "versionCode": 10112130531}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["@react-native-firebase/app", "@react-native-firebase/auth", "@react-native-firebase/crashlytics", "@react-native-firebase/messaging", "@react-native-firebase/perf", "expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], "expo-secure-store", ["expo-speech-recognition", {"microphonePermission": "Allow $(PRODUCT_NAME) to use the microphone.", "speechRecognitionPermission": "Allow $(PRODUCT_NAME) to use speech recognition.", "androidSpeechServicePackages": ["com.google.android.googlequicksearchbox"]}], ["expo-document-picker", {"iCloudContainerEnvironment": "Production"}], "expo-localization", ["expo-build-properties", {"ios": {"deploymentTarget": "16.2"}}], ["@sentry/react-native/expo", {"url": "https://sentry.io/", "project": "diogenes-ai-companion-react-native", "organization": "diogenes-consulting-llc"}]], "experiments": {"typedRoutes": true}, "extra": {"router": {"origin": false}, "eas": {"projectId": "80789c56-2509-41ad-aebf-24f5ac4f5641"}}, "owner": "simon6752"}}