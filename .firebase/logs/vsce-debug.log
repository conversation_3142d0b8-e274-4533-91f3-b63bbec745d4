[debug] [2025-01-13T03:49:22.692Z] [Firebase Plugin] Activating Firebase extension.
[info] [Firebase Plugin] Checked firebase-tools, is up to date!
[debug] [2025-01-13T03:49:41.952Z] [Firebase Plugin] VSCode notification server listening on port 40001
[debug] [2025-01-13T03:49:43.106Z] Object "" in "firebase.json" has unknown property: {"additionalProperty":"react-native"}
[info] [Firebase Plugin] Stopping Data Connect toolkit
[debug] [2025-01-14T17:54:43.591Z] [Firebase Plugin] Activating Firebase extension.
[info] [Firebase Plugin] Checked firebase-tools, is up to date!
[debug] [2025-01-14T17:55:33.254Z] Object "" in "firebase.json" has unknown property: {"additionalProperty":"react-native"}
[info] [Firebase Plugin] Stopping Data Connect toolkit
[debug] [2025-01-14T21:12:02.063Z] [Firebase Plugin] Activating Firebase extension.
[info] [Firebase Plugin] Checked firebase-tools, is up to date!
[info] [Firebase Plugin] Stopping Data Connect toolkit
[debug] [2025-01-24T22:30:59.346Z] [Firebase Plugin] Activating Firebase extension.
[debug] [2025-01-24T22:31:55.726Z] [Firebase Plugin] VSCode notification server listening on port 40001
[debug] [2025-01-24T22:31:58.918Z] Object "" in "firebase.json" has unknown property: {"additionalProperty":"react-native"}
[info] [Firebase Plugin] Stopping Data Connect toolkit
[debug] [2025-05-29T06:27:01.461Z] [Firebase Plugin] Activating Firebase extension.
[debug] [2025-05-29T06:27:18.716Z] [Firebase Plugin] requireAuthWrapper
[debug] [2025-05-29T06:27:18.717Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-05-29T06:27:18.718Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-05-29T06:27:18.721Z] Checked if tokens are valid: false, expires at: 1748372229614
[debug] [2025-05-29T06:27:18.722Z] Checked if tokens are valid: false, expires at: 1748372229614
[debug] [2025-05-29T06:27:18.722Z] > refreshing access token with scopes: []
[debug] [2025-05-29T06:27:18.724Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-05-29T06:27:18.724Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-05-29T06:27:18.734Z] [Firebase Plugin] VSCode notification server listening on port 40001
[debug] [2025-05-29T06:27:20.790Z] Object "" in "firebase.json" has unknown property: {"additionalProperty":"react-native"}
[debug] [2025-05-29T06:27:22.142Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-05-29T06:27:22.142Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-05-29T06:27:22.177Z] [Firebase Plugin] User found:  <EMAIL>
[info] [Firebase Plugin] (Core:Project) New user detected, fetching projects
[debug] [2025-05-29T06:27:22.180Z] Checked if tokens are valid: true, expires at: 1748503641142
[debug] [2025-05-29T06:27:22.182Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects pageSize=1000
[debug] [2025-05-29T06:27:27.537Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects 200
[debug] [2025-05-29T06:27:27.537Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects [omitted]
