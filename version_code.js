const fs = require('fs');
const path = require('path');

// Path to your app.json
const appJsonPath = path.join(__dirname, 'app.json');

// Read app.json safely
let appJson;
try {
  const data = fs.readFileSync(appJsonPath, 'utf-8');
  appJson = JSON.parse(data);  // Parse the JSON file
} catch (error) {
  console.error("Error reading or parsing app.json:", error);
  process.exit(1);
}

// Get current date and time
const now = new Date();

// Generate version code in the format (year-2024)(mmddhhmmss)
const yearAdjusted = now.getFullYear() - 2024;  // Adjust year by 2024
const month = (now.getMonth() + 1).toString().padStart(2, '0');  // Month in MM format
const day = now.getDate().toString().padStart(2, '0');  // Day in DD format
const hour = now.getHours().toString().padStart(2, '0');  // Hour in HH format
const minute = now.getMinutes().toString().padStart(2, '0');  // Minute in MM format
const second = now.getSeconds().toString().padStart(2, '0');  // Second in SS format

// Create the version code
const newVersionCode = `${yearAdjusted}${month}${day}${hour}${minute}${second}`;

// Update version and build number in app.json
appJson.expo.version = `1.0.${newVersionCode}`;  // Update version field
appJson.expo.android.versionCode = parseInt(newVersionCode, 10);  // Use new version code for Android
appJson.expo.ios.buildNumber = newVersionCode;  // Use new version code for iOS

// Write the updated app.json back to the file
fs.writeFileSync(appJsonPath, JSON.stringify(appJson, null, 2));

console.log(`Updated version to 1.0.${newVersionCode} and build number to ${newVersionCode}`);